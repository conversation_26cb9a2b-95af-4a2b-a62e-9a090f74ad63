{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "core/query-title", "title": "Query Title", "category": "theme", "description": "Display the query title.", "textdomain": "default", "attributes": {"type": {"type": "string"}, "textAlign": {"type": "string"}, "level": {"type": "number", "default": 1}, "levelOptions": {"type": "array"}, "showPrefix": {"type": "boolean", "default": true}, "showSearchTerm": {"type": "boolean", "default": true}}, "example": {"attributes": {"type": "search"}}, "supports": {"align": ["wide", "full"], "html": false, "color": {"gradients": true, "__experimentalDefaultControls": {"background": true, "text": true}}, "spacing": {"margin": true, "padding": true}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalFontStyle": true, "__experimentalFontWeight": true, "__experimentalLetterSpacing": true, "__experimentalTextTransform": true, "__experimentalTextDecoration": true, "__experimentalDefaultControls": {"fontSize": true}}, "interactivity": {"clientNavigation": true}, "__experimentalBorder": {"radius": true, "color": true, "width": true, "style": true, "__experimentalDefaultControls": {"radius": true, "color": true, "width": true, "style": true}}}, "style": "wp-block-query-title"}