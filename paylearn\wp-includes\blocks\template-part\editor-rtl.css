.block-editor-template-part__selection-modal{
  z-index:1000001;
}
.block-editor-template-part__selection-modal .block-editor-block-patterns-list{
  column-count:2;
  column-gap:24px;
}
@media (min-width:1280px){
  .block-editor-template-part__selection-modal .block-editor-block-patterns-list{
    column-count:3;
  }
}
.block-editor-template-part__selection-modal .block-editor-block-patterns-list .block-editor-block-patterns-list__list-item{
  break-inside:avoid-column;
}

.block-library-template-part__selection-search{
  background:#fff;
  padding:16px 0;
  position:sticky;
  top:0;
  z-index:2;
}
.block-editor-block-list__block:not(.remove-outline).is-reusable.block-editor-block-list__block:not([contenteditable]):focus:after,.block-editor-block-list__block:not(.remove-outline).is-reusable.is-highlighted:after,.block-editor-block-list__block:not(.remove-outline).is-reusable.is-selected:after,.block-editor-block-list__block:not(.remove-outline).wp-block-template-part.block-editor-block-list__block:not([contenteditable]):focus:after,.block-editor-block-list__block:not(.remove-outline).wp-block-template-part.is-highlighted:after,.block-editor-block-list__block:not(.remove-outline).wp-block-template-part.is-selected:after{
  outline-color:var(--wp-block-synced-color);
}

.is-outline-mode .block-editor-block-list__block:not(.remove-outline).wp-block-template-part.has-editable-outline:after{
  border:none;
}