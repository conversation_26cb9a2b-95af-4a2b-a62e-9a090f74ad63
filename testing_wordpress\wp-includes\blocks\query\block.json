{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "core/query", "title": "Query Loop", "category": "theme", "description": "An advanced block that allows displaying post types based on different query parameters and visual configurations.", "keywords": ["posts", "list", "blog", "blogs", "custom post types"], "textdomain": "default", "attributes": {"queryId": {"type": "number"}, "query": {"type": "object", "default": {"perPage": null, "pages": 0, "offset": 0, "postType": "post", "order": "desc", "orderBy": "date", "author": "", "search": "", "exclude": [], "sticky": "", "inherit": true, "taxQuery": null, "parents": [], "format": []}}, "tagName": {"type": "string", "default": "div"}, "namespace": {"type": "string"}, "enhancedPagination": {"type": "boolean", "default": false}}, "usesContext": ["templateSlug"], "providesContext": {"queryId": "queryId", "query": "query", "displayLayout": "displayLayout", "enhancedPagination": "enhancedPagination"}, "supports": {"align": ["wide", "full"], "html": false, "layout": true, "interactivity": true}, "editorStyle": "wp-block-query-editor"}