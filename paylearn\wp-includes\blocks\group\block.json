{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "core/group", "title": "Group", "category": "design", "description": "Gather blocks in a layout container.", "keywords": ["container", "wrapper", "row", "section"], "textdomain": "default", "attributes": {"tagName": {"type": "string", "default": "div"}, "templateLock": {"type": ["string", "boolean"], "enum": ["all", "insert", "contentOnly", false]}, "allowedBlocks": {"type": "array"}}, "supports": {"__experimentalOnEnter": true, "__experimentalOnMerge": true, "__experimentalSettings": true, "align": ["wide", "full"], "anchor": true, "ariaLabel": true, "html": false, "background": {"backgroundImage": true, "backgroundSize": true, "__experimentalDefaultControls": {"backgroundImage": true}}, "color": {"gradients": true, "heading": true, "button": true, "link": true, "__experimentalDefaultControls": {"background": true, "text": true}}, "shadow": true, "spacing": {"margin": ["top", "bottom"], "padding": true, "blockGap": true, "__experimentalDefaultControls": {"padding": true, "blockGap": true}}, "dimensions": {"minHeight": true}, "__experimentalBorder": {"color": true, "radius": true, "style": true, "width": true, "__experimentalDefaultControls": {"color": true, "radius": true, "style": true, "width": true}}, "position": {"sticky": true}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalFontWeight": true, "__experimentalFontStyle": true, "__experimentalTextTransform": true, "__experimentalTextDecoration": true, "__experimentalLetterSpacing": true, "__experimentalDefaultControls": {"fontSize": true}}, "layout": {"allowSizingOnChildren": true}, "interactivity": {"clientNavigation": true}}, "editorStyle": "wp-block-group-editor", "style": "wp-block-group"}