.block-library-query-pattern__selection-modal .block-editor-block-patterns-list{
  column-count:2;
  column-gap:24px;
}
@media (min-width:1280px){
  .block-library-query-pattern__selection-modal .block-editor-block-patterns-list{
    column-count:3;
  }
}
.block-library-query-pattern__selection-modal .block-editor-block-patterns-list .block-editor-block-patterns-list__list-item{
  break-inside:avoid-column;
}
.block-library-query-pattern__selection-modal .block-library-query-pattern__selection-search{
  background:#fff;
  margin-bottom:-4px;
  padding:16px 0;
  position:sticky;
  top:0;
  transform:translateY(-4px);
  z-index:2;
}

@media (min-width:600px){
  .wp-block-query__enhanced-pagination-modal{
    max-width:480px;
  }
}

.block-editor-block-settings-menu__popover.is-expanded{
  overflow-y:scroll;
}
.block-editor-block-settings-menu__popover .block-library-query-pattern__selection-content{
  height:100%;
}
.block-editor-block-settings-menu__popover .block-editor-block-patterns-list{
  display:grid;
  grid-template-columns:1fr;
  grid-gap:12px;
  min-width:280px;
}
@media (min-width:600px){
  .block-editor-block-settings-menu__popover .block-editor-block-patterns-list{
    grid-template-columns:1fr 1fr;
    min-width:480px;
  }
}
.block-editor-block-settings-menu__popover .block-editor-block-patterns-list__list-item{
  margin-bottom:0;
}