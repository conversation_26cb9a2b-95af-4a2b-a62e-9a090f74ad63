{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "core/post-excerpt", "title": "Excerpt", "category": "theme", "description": "Display the excerpt.", "textdomain": "default", "attributes": {"textAlign": {"type": "string"}, "moreText": {"type": "string"}, "showMoreOnNewLine": {"type": "boolean", "default": true}, "excerptLength": {"type": "number", "default": 55}}, "usesContext": ["postId", "postType", "queryId"], "example": {"viewportWidth": 350}, "supports": {"html": false, "color": {"gradients": true, "link": true, "__experimentalDefaultControls": {"background": true, "text": true, "link": true}}, "spacing": {"margin": true, "padding": true}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalFontWeight": true, "__experimentalFontStyle": true, "__experimentalTextTransform": true, "__experimentalTextDecoration": true, "__experimentalLetterSpacing": true, "__experimentalDefaultControls": {"fontSize": true}}, "interactivity": {"clientNavigation": true}, "__experimentalBorder": {"radius": true, "color": true, "width": true, "style": true, "__experimentalDefaultControls": {"radius": true, "color": true, "width": true, "style": true}}}, "editorStyle": "wp-block-post-excerpt-editor", "style": "wp-block-post-excerpt"}