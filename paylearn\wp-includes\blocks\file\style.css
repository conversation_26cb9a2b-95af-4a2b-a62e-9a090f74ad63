.wp-block-file{
  box-sizing:border-box;
}
.wp-block-file:not(.wp-element-button){
  font-size:.8em;
}
.wp-block-file.aligncenter{
  text-align:center;
}
.wp-block-file.alignright{
  text-align:right;
}
.wp-block-file *+.wp-block-file__button{
  margin-left:.75em;
}

:where(.wp-block-file){
  margin-bottom:1.5em;
}

.wp-block-file__embed{
  margin-bottom:1em;
}

:where(.wp-block-file__button){
  border-radius:2em;
  display:inline-block;
  padding:.5em 1em;
}
:where(.wp-block-file__button):is(a):active,:where(.wp-block-file__button):is(a):focus,:where(.wp-block-file__button):is(a):hover,:where(.wp-block-file__button):is(a):visited{
  box-shadow:none;
  color:#fff;
  opacity:.85;
  text-decoration:none;
}