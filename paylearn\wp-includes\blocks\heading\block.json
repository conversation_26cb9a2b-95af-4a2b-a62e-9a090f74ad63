{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "core/heading", "title": "Heading", "category": "text", "description": "Introduce new sections and organize content to help visitors (and search engines) understand the structure of your content.", "keywords": ["title", "subtitle"], "textdomain": "default", "attributes": {"textAlign": {"type": "string"}, "content": {"type": "rich-text", "source": "rich-text", "selector": "h1,h2,h3,h4,h5,h6", "role": "content"}, "level": {"type": "number", "default": 2}, "levelOptions": {"type": "array"}, "placeholder": {"type": "string"}}, "supports": {"align": ["wide", "full"], "anchor": true, "className": true, "splitting": true, "__experimentalBorder": {"color": true, "radius": true, "style": true, "width": true}, "color": {"gradients": true, "link": true, "__experimentalDefaultControls": {"background": true, "text": true}}, "spacing": {"margin": true, "padding": true, "__experimentalDefaultControls": {"margin": false, "padding": false}}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalFontStyle": true, "__experimentalFontWeight": true, "__experimentalLetterSpacing": true, "__experimentalTextTransform": true, "__experimentalTextDecoration": true, "__experimentalWritingMode": true, "__experimentalDefaultControls": {"fontSize": true}}, "__unstablePasteTextInline": true, "__experimentalSlashInserter": true, "interactivity": {"clientNavigation": true}}, "editorStyle": "wp-block-heading-editor", "style": "wp-block-heading"}