{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "core/comments-pagination-previous", "title": "Comments Previous Page", "category": "theme", "parent": ["core/comments-pagination"], "description": "Displays the previous comment's page link.", "textdomain": "default", "attributes": {"label": {"type": "string"}}, "usesContext": ["postId", "comments/paginationArrow"], "supports": {"reusable": false, "html": false, "color": {"gradients": true, "text": false, "__experimentalDefaultControls": {"background": true}}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalFontWeight": true, "__experimentalFontStyle": true, "__experimentalTextTransform": true, "__experimentalTextDecoration": true, "__experimentalLetterSpacing": true, "__experimentalDefaultControls": {"fontSize": true}}, "interactivity": {"clientNavigation": true}}}