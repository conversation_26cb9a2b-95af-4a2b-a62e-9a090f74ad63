{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "core/quote", "title": "Quote", "category": "text", "description": "Give quoted text visual emphasis. \"In quoting others, we cite ourselves.\" — <PERSON>", "keywords": ["blockquote", "cite"], "textdomain": "default", "attributes": {"value": {"type": "string", "source": "html", "selector": "blockquote", "multiline": "p", "default": "", "role": "content"}, "citation": {"type": "rich-text", "source": "rich-text", "selector": "cite", "role": "content"}, "textAlign": {"type": "string"}}, "supports": {"anchor": true, "align": ["left", "right", "wide", "full"], "html": false, "background": {"backgroundImage": true, "backgroundSize": true, "__experimentalDefaultControls": {"backgroundImage": true}}, "__experimentalBorder": {"color": true, "radius": true, "style": true, "width": true, "__experimentalDefaultControls": {"color": true, "radius": true, "style": true, "width": true}}, "dimensions": {"minHeight": true, "__experimentalDefaultControls": {"minHeight": false}}, "__experimentalOnEnter": true, "__experimentalOnMerge": true, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalFontWeight": true, "__experimentalFontStyle": true, "__experimentalTextTransform": true, "__experimentalTextDecoration": true, "__experimentalLetterSpacing": true, "__experimentalDefaultControls": {"fontSize": true}}, "color": {"gradients": true, "heading": true, "link": true, "__experimentalDefaultControls": {"background": true, "text": true}}, "layout": {"allowEditing": false}, "spacing": {"blockGap": true, "padding": true, "margin": true}, "interactivity": {"clientNavigation": true}}, "styles": [{"name": "default", "label": "<PERSON><PERSON><PERSON>", "isDefault": true}, {"name": "plain", "label": "Plain"}], "editorStyle": "wp-block-quote-editor", "style": "wp-block-quote"}