{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "core/details", "title": "Details", "category": "text", "description": "Hide and show additional content.", "keywords": ["accordion", "summary", "toggle", "disclosure"], "textdomain": "default", "attributes": {"showContent": {"type": "boolean", "default": false}, "summary": {"type": "rich-text", "source": "rich-text", "selector": "summary"}, "name": {"type": "string", "source": "attribute", "attribute": "name", "selector": ".wp-block-details"}, "allowedBlocks": {"type": "array"}, "placeholder": {"type": "string"}}, "supports": {"__experimentalOnEnter": true, "align": ["wide", "full"], "anchor": true, "color": {"gradients": true, "link": true, "__experimentalDefaultControls": {"background": true, "text": true}}, "__experimentalBorder": {"color": true, "width": true, "style": true}, "html": false, "spacing": {"margin": true, "padding": true, "blockGap": true, "__experimentalDefaultControls": {"margin": false, "padding": false}}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalFontWeight": true, "__experimentalFontStyle": true, "__experimentalTextTransform": true, "__experimentalTextDecoration": true, "__experimentalLetterSpacing": true, "__experimentalDefaultControls": {"fontSize": true}}, "layout": {"allowEditing": false}, "interactivity": {"clientNavigation": true}}, "editorStyle": "wp-block-details-editor", "style": "wp-block-details"}