[2023-08-14 12:57:38] merlin-logger.DEBUG: The welcome step has been displayed [] []
[2023-08-14 12:57:46] merlin-logger.DEBUG: The plugin installation step has been displayed [] []
[2023-08-14 12:57:59] merlin-logger.DEBUG: A plugin with the following data will be processed {"plugin_slug":"elementor","message":"Updating"} []
[2023-08-14 12:58:18] merlin-logger.DEBUG: A plugin with the following data was processed {"plugin_slug":"elementor"} []
[2023-08-14 12:58:24] merlin-logger.DEBUG: The content import step has been displayed [] []
[2023-08-14 12:58:34] merlin-logger.INFO: The content import AJAX call will be executed with this import data {"title":"Demo Content","data":"/home/<USER>/public_html/wordpress/wp-content/uploads/2023/08/content-2023-08-14__12-58-32.xml"} []
[2023-08-14 12:58:34] merlin-logger.INFO: Imported "6548" (stm_lms_question_taxonomy) [] []
[2023-08-14 12:58:34] merlin-logger.DEBUG: Term 100 remapped to 197 [] []
[2023-08-14 12:58:34] merlin-logger.INFO: Imported "Analysis of Algorithms" (stm_lms_course_taxonomy) [] []
[2023-08-14 12:58:34] merlin-logger.DEBUG: Term 73 remapped to 198 [] []
[2023-08-14 12:58:34] merlin-logger.INFO: Imported "Art" (stm_lms_course_taxonomy) [] []
[2023-08-14 12:58:34] merlin-logger.DEBUG: Term 44 remapped to 199 [] []
[2023-08-14 12:58:34] merlin-logger.INFO: Imported "Bicycling" (stm_lms_course_taxonomy) [] []
[2023-08-14 12:58:34] merlin-logger.DEBUG: Term 48 remapped to 200 [] []
[2023-08-14 12:58:34] merlin-logger.INFO: Imported "C++" (stm_lms_course_taxonomy) [] []
[2023-08-14 12:58:34] merlin-logger.DEBUG: Term 39 remapped to 201 [] []
[2023-08-14 12:58:34] merlin-logger.INFO: Imported "Communication" (stm_lms_course_taxonomy) [] []
[2023-08-14 12:58:34] merlin-logger.DEBUG: Term 53 remapped to 202 [] []
[2023-08-14 12:58:34] merlin-logger.INFO: Imported "Conceptual Art" (stm_lms_course_taxonomy) [] []
[2023-08-14 12:58:34] merlin-logger.DEBUG: Term 46 remapped to 203 [] []
[2023-08-14 12:58:34] merlin-logger.INFO: Imported "Electronic" (stm_lms_course_taxonomy) [] []
[2023-08-14 12:58:34] merlin-logger.DEBUG: Term 25 remapped to 204 [] []
[2023-08-14 12:58:34] merlin-logger.INFO: Imported "Fashion" (stm_lms_course_taxonomy) [] []
[2023-08-14 12:58:34] merlin-logger.DEBUG: Term 54 remapped to 205 [] []
[2023-08-14 12:58:34] merlin-logger.INFO: Imported "Landscape" (stm_lms_course_taxonomy) [] []
[2023-08-14 12:58:34] merlin-logger.DEBUG: Term 35 remapped to 206 [] []
[2023-08-14 12:58:34] merlin-logger.INFO: Imported "Photography" (stm_lms_course_taxonomy) [] []
[2023-08-14 12:58:34] merlin-logger.DEBUG: Term 20 remapped to 207 [] []
[2023-08-14 12:58:34] merlin-logger.INFO: Imported "PHP, CSS, JS" (stm_lms_course_taxonomy) [] []
[2023-08-14 12:58:34] merlin-logger.DEBUG: Term 41 remapped to 208 [] []
[2023-08-14 12:58:34] merlin-logger.INFO: Imported "Software Development" (stm_lms_course_taxonomy) [] []
[2023-08-14 12:58:34] merlin-logger.DEBUG: Term 91 remapped to 209 [] []
[2023-08-14 12:58:34] merlin-logger.INFO: Imported "Soul Music" (stm_lms_course_taxonomy) [] []
[2023-08-14 12:58:34] merlin-logger.DEBUG: Term 33 remapped to 210 [] []
[2023-08-14 12:58:34] merlin-logger.WARNING: Failed to import product_type stm_lms_product [] []
[2023-08-14 12:58:34] merlin-logger.DEBUG: Invalid taxonomy. [] []
[2023-08-14 12:58:34] merlin-logger.INFO: Imported "MS LMS Starter Theme Main Menu" (nav_menu) [] []
[2023-08-14 12:58:34] merlin-logger.DEBUG: Term 59 remapped to 211 [] []
[2023-08-14 12:58:35] merlin-logger.INFO: Imported "placeholder.gif" (Media) [] []
[2023-08-14 12:58:35] merlin-logger.DEBUG: Post 1669 remapped to 1669 [] []
[2023-08-14 12:58:35] merlin-logger.INFO: Imported "Rectangle-1.png" (Media) [] []
[2023-08-14 12:58:35] merlin-logger.DEBUG: Post 1673 remapped to 1673 [] []
[2023-08-14 12:58:36] merlin-logger.INFO: Imported "Mask-Group-29-1.jpg" (Media) [] []
[2023-08-14 12:58:36] merlin-logger.DEBUG: Post 1691 remapped to 1691 [] []
[2023-08-14 12:58:36] merlin-logger.INFO: Imported "Image_27-1.jpeg" (Media) [] []
[2023-08-14 12:58:36] merlin-logger.DEBUG: Post 1692 remapped to 1692 [] []
[2023-08-14 12:58:37] merlin-logger.INFO: Imported "featured_image-1.jpg" (Media) [] []
[2023-08-14 12:58:37] merlin-logger.DEBUG: Post 1693 remapped to 1693 [] []
[2023-08-14 12:58:37] merlin-logger.INFO: Imported "Image_29-1.png" (Media) [] []
[2023-08-14 12:58:37] merlin-logger.DEBUG: Post 1694 remapped to 1694 [] []
[2023-08-14 12:58:37] merlin-logger.INFO: Imported "photo-1475452779376-caebfb988090" (Media) [] []
[2023-08-14 12:58:37] merlin-logger.DEBUG: Post 46828 remapped to 46828 [] []
[2023-08-14 12:58:38] merlin-logger.INFO: Imported "photo-1461749280684-dccba630e2f6" (Media) [] []
[2023-08-14 12:58:38] merlin-logger.DEBUG: Post 46829 remapped to 46829 [] []
[2023-08-14 12:58:39] merlin-logger.INFO: Imported "msbasics" (Media) [] []
[2023-08-14 12:58:39] merlin-logger.DEBUG: Post 46868 remapped to 46868 [] []
[2023-08-14 12:58:39] merlin-logger.INFO: Imported "logo@2x" (Media) [] []
[2023-08-14 12:58:39] merlin-logger.DEBUG: Post 48291 remapped to 48291 [] []
[2023-08-14 12:58:40] merlin-logger.INFO: Imported "staff_3-350x250" (Media) [] []
[2023-08-14 12:58:40] merlin-logger.DEBUG: Post 48299 remapped to 48299 [] []
[2023-08-14 12:58:40] merlin-logger.INFO: Imported "staff_2-350x250" (Media) [] []
[2023-08-14 12:58:40] merlin-logger.DEBUG: Post 48300 remapped to 48300 [] []
[2023-08-14 12:58:40] merlin-logger.INFO: Imported "staff_1-350x250" (Media) [] []
[2023-08-14 12:58:40] merlin-logger.DEBUG: Post 48301 remapped to 48301 [] []
[2023-08-14 12:58:40] merlin-logger.INFO: Imported "Shape-1-1" (Media) [] []
[2023-08-14 12:58:40] merlin-logger.DEBUG: Post 48320 remapped to 48320 [] []
[2023-08-14 12:58:41] merlin-logger.INFO: Imported "Shape-1" (Media) [] []
[2023-08-14 12:58:41] merlin-logger.DEBUG: Post 48321 remapped to 48321 [] []
[2023-08-14 12:58:41] merlin-logger.INFO: Imported "ms_coin_b" (Media) [] []
[2023-08-14 12:58:41] merlin-logger.DEBUG: Post 48383 remapped to 48383 [] []
[2023-08-14 12:58:42] merlin-logger.INFO: Imported "NYC-Department-of-Education-School-Technology-Summit-2015-1000x500-1-870x440" (Media) [] []
[2023-08-14 12:58:42] merlin-logger.DEBUG: Post 48391 remapped to 48391 [] []
[2023-08-14 12:58:42] merlin-logger.INFO: Imported "photo-1491897554428-130a60dd4757-870x440" (Media) [] []
[2023-08-14 12:58:42] merlin-logger.DEBUG: Post 48394 remapped to 48394 [] []
[2023-08-14 12:58:42] merlin-logger.INFO: Imported "curtis-macnewton-12711-unsplash-min" (Media) [] []
[2023-08-14 12:58:42] merlin-logger.DEBUG: Post 48508 remapped to 48508 [] []
[2023-08-14 12:58:43] merlin-logger.INFO: Imported "cristian-grecu-762012-unsplash-min" (Media) [] []
[2023-08-14 12:58:43] merlin-logger.DEBUG: Post 48509 remapped to 48509 [] []
[2023-08-14 12:58:43] merlin-logger.INFO: Imported "Default Kit" (Template) [] []
[2023-08-14 12:58:43] merlin-logger.DEBUG: Post 5 remapped to 48510 [] []
[2023-08-14 12:58:43] merlin-logger.INFO: Imported "home live" (Template) [] []
[2023-08-14 12:58:43] merlin-logger.DEBUG: Post 1695 remapped to 1695 [] []
[2023-08-14 12:58:44] merlin-logger.INFO: Imported "header" (Template) [] []
[2023-08-14 12:58:44] merlin-logger.DEBUG: Post 48145 remapped to 48145 [] []
[2023-08-14 12:58:44] merlin-logger.INFO: Imported "footer" (Template) [] []
[2023-08-14 12:58:44] merlin-logger.DEBUG: Post 48148 remapped to 48148 [] []
[2023-08-14 12:58:44] merlin-logger.INFO: Imported "Starter MS" (Template) [] []
[2023-08-14 12:58:44] merlin-logger.DEBUG: Post 48162 remapped to 48162 [] []
[2023-08-14 12:58:45] merlin-logger.INFO: Imported "jakob-owens-198234-unsplash-min-1" (Media) [] []
[2023-08-14 12:58:45] merlin-logger.DEBUG: Post 48510 remapped to 48511 [] []
[2023-08-14 12:58:45] merlin-logger.INFO: Imported "landscape" (Media) [] []
[2023-08-14 12:58:45] merlin-logger.DEBUG: Post 48511 remapped to 48512 [] []
[2023-08-14 12:58:46] merlin-logger.INFO: Imported "photo-1416339134316-0e91dc9ded92" (Media) [] []
[2023-08-14 12:58:46] merlin-logger.DEBUG: Post 48512 remapped to 48513 [] []
[2023-08-14 12:58:46] merlin-logger.INFO: Imported "photo-1496307042754-b4aa456c4a2d" (Media) [] []
[2023-08-14 12:58:46] merlin-logger.DEBUG: Post 48513 remapped to 48514 [] []
[2023-08-14 12:58:47] merlin-logger.INFO: Imported "photo-1499720843949-d9e6f318dca0-1" (Media) [] []
[2023-08-14 12:58:47] merlin-logger.DEBUG: Post 48514 remapped to 48515 [] []
[2023-08-14 12:58:47] merlin-logger.INFO: Imported "cathryn-lavery-67852-unsplash" (Media) [] []
[2023-08-14 12:58:47] merlin-logger.DEBUG: Post 48515 remapped to 48516 [] []
[2023-08-14 12:58:48] merlin-logger.INFO: Imported "photo-1512686096451-a15c19314d59" (Media) [] []
[2023-08-14 12:58:48] merlin-logger.DEBUG: Post 48536 remapped to 48536 [] []
[2023-08-14 12:58:48] merlin-logger.INFO: Imported "photo-1513829596324-4bb2800c5efb" (Media) [] []
[2023-08-14 12:58:48] merlin-logger.DEBUG: Post 48537 remapped to 48537 [] []
[2023-08-14 12:58:49] merlin-logger.INFO: Imported "photo-1525994886773-080587e161c2" (Media) [] []
[2023-08-14 12:58:49] merlin-logger.DEBUG: Post 48541 remapped to 48541 [] []
[2023-08-14 12:58:50] merlin-logger.INFO: Imported "jakob-owens-198234-unsplash-min-1-1024x664" (Media) [] []
[2023-08-14 12:58:50] merlin-logger.DEBUG: Post 48868 remapped to 48868 [] []
[2023-08-14 12:58:51] merlin-logger.INFO: Imported "photo-1416339134316-0e91dc9ded92-1024x767" (Media) [] []
[2023-08-14 12:58:51] merlin-logger.DEBUG: Post 48869 remapped to 48869 [] []
[2023-08-14 12:58:51] merlin-logger.INFO: Imported "photo-1496307042754-b4aa456c4a2d-1024x683" (Media) [] []
[2023-08-14 12:58:51] merlin-logger.DEBUG: Post 48870 remapped to 48870 [] []
[2023-08-14 12:58:52] merlin-logger.INFO: Imported "photo-1499720843949-d9e6f318dca0-1-1024x683" (Media) [] []
[2023-08-14 12:58:52] merlin-logger.DEBUG: Post 48871 remapped to 48871 [] []
[2023-08-14 12:58:52] merlin-logger.INFO: Imported "photo-1512686096451-a15c19314d59-1024x683" (Media) [] []
[2023-08-14 12:58:52] merlin-logger.DEBUG: Post 48873 remapped to 48873 [] []
[2023-08-14 12:58:53] merlin-logger.INFO: Imported "woocommerce-placeholder" (Media) [] []
[2023-08-14 12:58:53] merlin-logger.DEBUG: Post 48923 remapped to 48923 [] []
[2023-08-14 12:58:53] merlin-logger.INFO: Imported "Hello world!" (Post) [] []
[2023-08-14 12:58:53] merlin-logger.DEBUG: Post 1 remapped to 48924 [] []
[2023-08-14 12:58:53] merlin-logger.INFO: Imported "Checkout" (Page) [] []
[2023-08-14 12:58:53] merlin-logger.DEBUG: Post 12 remapped to 48925 [] []
[2023-08-14 12:58:53] merlin-logger.INFO: Imported "Courses Page" (Page) [] []
[2023-08-14 12:58:53] merlin-logger.DEBUG: Post 13 remapped to 48926 [] []
[2023-08-14 12:58:53] merlin-logger.INFO: Imported "User Account" (Page) [] []
[2023-08-14 12:58:53] merlin-logger.DEBUG: Post 14 remapped to 48927 [] []
[2023-08-14 12:58:53] merlin-logger.INFO: Imported "User Public Account" (Page) [] []
[2023-08-14 12:58:53] merlin-logger.DEBUG: Post 15 remapped to 48928 [] []
[2023-08-14 12:58:53] merlin-logger.INFO: Imported "Wishlist" (Page) [] []
[2023-08-14 12:58:53] merlin-logger.DEBUG: Post 16 remapped to 48929 [] []
[2023-08-14 12:58:53] merlin-logger.INFO: Imported "Courses" (Navigation Menu Item) [] []
[2023-08-14 12:58:53] merlin-logger.DEBUG: Post 1689 remapped to 1689 [] []
[2023-08-14 12:58:53] merlin-logger.DEBUG: Processing menu item post_type [] []
[2023-08-14 12:58:53] merlin-logger.DEBUG: Menu item 13 mapped to 48926 [] []
[2023-08-14 12:58:53] merlin-logger.INFO: Imported "header" (Template) [] []
[2023-08-14 12:58:53] merlin-logger.DEBUG: Post 48164 remapped to 48164 [] []
[2023-08-14 12:58:53] merlin-logger.INFO: Imported "footer" (Template) [] []
[2023-08-14 12:58:53] merlin-logger.DEBUG: Post 48166 remapped to 48166 [] []
[2023-08-14 12:58:53] merlin-logger.INFO: Imported "home live" (Template) [] []
[2023-08-14 12:58:53] merlin-logger.DEBUG: Post 48168 remapped to 48168 [] []
[2023-08-14 12:58:53] merlin-logger.INFO: Imported "Home" (Page) [] []
[2023-08-14 12:58:53] merlin-logger.DEBUG: Post 48170 remapped to 48170 [] []
[2023-08-14 12:58:53] merlin-logger.INFO: Imported "footer" (Elementor Header & Footer Builder) [] []
[2023-08-14 12:58:53] merlin-logger.DEBUG: Post 48178 remapped to 48178 [] []
[2023-08-14 12:58:53] merlin-logger.INFO: Imported "header" (Elementor Header & Footer Builder) [] []
[2023-08-14 12:58:53] merlin-logger.DEBUG: Post 48181 remapped to 48181 [] []
[2023-08-14 12:58:53] merlin-logger.INFO: Imported "Hello world!" (Post) [] []
[2023-08-14 12:58:53] merlin-logger.DEBUG: Post 48212 remapped to 48212 [] []
[2023-08-14 12:58:53] merlin-logger.INFO: Post "Test post" already exists. [] []
[2023-08-14 12:58:53] merlin-logger.INFO: Imported "Home" (Navigation Menu Item) [] []
[2023-08-14 12:58:53] merlin-logger.DEBUG: Post 48266 remapped to 48266 [] []
[2023-08-14 12:58:53] merlin-logger.DEBUG: Processing menu item custom [] []
[2023-08-14 12:58:53] merlin-logger.DEBUG: Menu item 48266 mapped to 48266 [] []
[2023-08-14 12:58:53] merlin-logger.INFO: Imported "Add Course" (Navigation Menu Item) [] []
[2023-08-14 12:58:53] merlin-logger.DEBUG: Post 48269 remapped to 48269 [] []
[2023-08-14 12:58:53] merlin-logger.DEBUG: Processing menu item custom [] []
[2023-08-14 12:58:53] merlin-logger.DEBUG: Menu item 48269 mapped to 48269 [] []
[2023-08-14 12:58:53] merlin-logger.WARNING: Failed to import "The Complete Stock Trading Bundle Course": Invalid post type stm-course-bundles [] []
[2023-08-14 12:58:53] merlin-logger.WARNING: Failed to import "Enterprise for Real Things Art Painting by Jason Ni": Invalid post type product [] []
[2023-08-14 12:58:54] merlin-logger.INFO: Imported "" (Navigation Menu Item) [] []
[2023-08-14 12:58:54] merlin-logger.DEBUG: Post 49087 remapped to 49087 [] []
[2023-08-14 12:58:54] merlin-logger.DEBUG: Processing menu item post_type [] []
[2023-08-14 12:58:54] merlin-logger.DEBUG: Menu item 16 mapped to 48929 [] []
[2023-08-14 12:58:54] merlin-logger.INFO: Imported "Minimalism, How to make things simpler" (Course) [] []
[2023-08-14 12:58:54] merlin-logger.DEBUG: Post 868 remapped to 868 [] []
[2023-08-14 12:58:54] merlin-logger.INFO: Imported "How to get comfortable on camera" (Course) [] []
[2023-08-14 12:58:54] merlin-logger.DEBUG: Post 869 remapped to 869 [] []
[2023-08-14 12:58:54] merlin-logger.INFO: Imported "Web Coding and Apache Basics theory" (Course) [] []
[2023-08-14 12:58:54] merlin-logger.DEBUG: Post 882 remapped to 49088 [] []
[2023-08-14 12:58:54] merlin-logger.INFO: Imported "How to Make Beautiful Landscape photos?" (Course) [] []
[2023-08-14 12:58:54] merlin-logger.DEBUG: Post 996 remapped to 996 [] []
[2023-08-14 12:58:54] merlin-logger.INFO: New AJAX call! [] []
[2023-08-14 12:58:57] merlin-logger.INFO: The content import AJAX call will be executed with this import data {"title":"Demo Content","data":"/home/<USER>/public_html/wordpress/wp-content/uploads/2023/08/content-2023-08-14__12-58-32.xml"} []
[2023-08-14 12:58:57] merlin-logger.DEBUG: The Hello world post status was set to draft [] []
[2023-08-14 12:58:57] merlin-logger.WARNING: Failed to import product_type stm_lms_product [] []
[2023-08-14 12:58:57] merlin-logger.DEBUG: Invalid taxonomy. [] []
[2023-08-14 12:58:57] merlin-logger.INFO: Post "Test post" already exists. [] []
[2023-08-14 12:58:57] merlin-logger.WARNING: Failed to import "The Complete Stock Trading Bundle Course": Invalid post type stm-course-bundles [] []
[2023-08-14 12:58:57] merlin-logger.WARNING: Failed to import "Enterprise for Real Things Art Painting by Jason Ni": Invalid post type product [] []
[2023-08-14 12:58:57] merlin-logger.INFO: Imported "Real Things Art Painting by Jason Ni" (Course) [] []
[2023-08-14 12:58:57] merlin-logger.DEBUG: Post 997 remapped to 997 [] []
[2023-08-14 12:58:57] merlin-logger.INFO: Imported "Road Bike Manual or How to Be a Champion" (Course) [] []
[2023-08-14 12:58:57] merlin-logger.DEBUG: Post 999 remapped to 999 [] []
[2023-08-14 12:58:57] merlin-logger.INFO: Imported "Graphic Design Basics Masterclass" (Course) [] []
[2023-08-14 12:58:57] merlin-logger.DEBUG: Post 1001 remapped to 1001 [] []
[2023-08-14 12:58:57] merlin-logger.INFO: Imported "Fashion Photography from professional" (Course) [] []
[2023-08-14 12:58:57] merlin-logger.DEBUG: Post 1002 remapped to 1002 [] []
[2023-08-14 12:58:57] merlin-logger.INFO: Imported "How to be a DJ? Make Electronic Music" (Course) [] []
[2023-08-14 12:58:57] merlin-logger.DEBUG: Post 1073 remapped to 1073 [] []
[2023-08-14 12:58:58] merlin-logger.INFO: Imported "Part 1 - Your First Ride" (Lesson) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 47961 remapped to 47961 [] []
[2023-08-14 12:58:58] merlin-logger.INFO: Imported "Part 2 - A Closer Introduction To The Necessary Tools And Knowledge" (Lesson) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 47962 remapped to 47962 [] []
[2023-08-14 12:58:58] merlin-logger.INFO: Imported "Part 3 - Structure Your Training" (Lesson) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 47963 remapped to 47963 [] []
[2023-08-14 12:58:58] merlin-logger.INFO: Imported "Part 4 - Finding New Training Grounds" (Lesson) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 47964 remapped to 47964 [] []
[2023-08-14 12:58:58] merlin-logger.INFO: Imported "Lecture 2: What's Your Story?" (Lesson) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 47971 remapped to 47971 [] []
[2023-08-14 12:58:58] merlin-logger.INFO: Imported "Lecture 3 - The Illusion of Space Stream" (Lesson) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 47972 remapped to 47972 [] []
[2023-08-14 12:58:58] merlin-logger.INFO: Imported "Lecture 4 – Best Ways to Create Illusion" (Lesson) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 47973 remapped to 47973 [] []
[2023-08-14 12:58:58] merlin-logger.WARNING: Failed to import "The Complete Communication Skills Master Class for Life": Invalid post type stm-course-bundles [] []
[2023-08-14 12:58:58] merlin-logger.WARNING: Failed to import "Master “Technical Analysis and Chart reading skills” Bundle": Invalid post type stm-course-bundles [] []
[2023-08-14 12:58:58] merlin-logger.WARNING: Failed to import "The Complete Business Plan Course (Includes 5 Courses)": Invalid post type stm-course-bundles [] []
[2023-08-14 12:58:58] merlin-logger.WARNING: Failed to import "Web Development for Beginners 5-in-1 Course Bundle": Invalid post type stm-course-bundles [] []
[2023-08-14 12:58:58] merlin-logger.INFO: Imported "The most famous personalities in IT?" (Question) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 3762 remapped to 3762 [] []
[2023-08-14 12:58:58] merlin-logger.INFO: Imported "What does CPU stand for?" (Question) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 3766 remapped to 3766 [] []
[2023-08-14 12:58:58] merlin-logger.INFO: Imported "Fill the Gap" (Question) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 3769 remapped to 3769 [] []
[2023-08-14 12:58:58] merlin-logger.INFO: Imported "Which of these is not an early computer?" (Question) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 3770 remapped to 3770 [] []
[2023-08-14 12:58:58] merlin-logger.INFO: Imported "Match Actors Names" (Question) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 3772 remapped to 3772 [] []
[2023-08-14 12:58:58] merlin-logger.INFO: Imported "Type all computer parts you know" (Question) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 3773 remapped to 3773 [] []
[2023-08-14 12:58:58] merlin-logger.INFO: Imported "Is 380MBps transfer rate of a standard USB 2.0 Device?" (Question) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 3774 remapped to 3774 [] []
[2023-08-14 12:58:58] merlin-logger.INFO: Imported "Which of the indicated programming languages ​​can create mobile sentences" (Question) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 3775 remapped to 3775 [] []
[2023-08-14 12:58:58] merlin-logger.INFO: Imported "The most famous personalities in IT?" (Question) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 3776 remapped to 3776 [] []
[2023-08-14 12:58:58] merlin-logger.INFO: Imported "What is the most common OS in the world?" (Question) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 3779 remapped to 3779 [] []
[2023-08-14 12:58:58] merlin-logger.INFO: Imported "What does CPU stand for?" (Question) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 3780 remapped to 3780 [] []
[2023-08-14 12:58:58] merlin-logger.INFO: Imported "Lecture 1: Ideas About Painting" (Lesson) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 47970 remapped to 47970 [] []
[2023-08-14 12:58:58] merlin-logger.INFO: Imported "Middle Quiz" (Quiz) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 48097 remapped to 48097 [] []
[2023-08-14 12:58:58] merlin-logger.INFO: Imported "Custom Styles" (Global Styles) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 48140 remapped to 48140 [] []
[2023-08-14 12:58:58] merlin-logger.INFO: Imported "Custom Styles" (Global Styles) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 48398 remapped to 48398 [] []
[2023-08-14 12:58:58] merlin-logger.INFO: Imported "Part 5 - Zoom Conference" (Lesson) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 48742 remapped to 48742 [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Running post-processing for post 1669 [] []
[2023-08-14 12:58:58] merlin-logger.WARNING: Could not find the author for "placeholder.gif" (post #1669) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 1669 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 1669 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Running post-processing for post 1673 [] []
[2023-08-14 12:58:58] merlin-logger.WARNING: Could not find the author for "Rectangle-1.png" (post #1673) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 1673 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 1673 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Running post-processing for post 1691 [] []
[2023-08-14 12:58:58] merlin-logger.WARNING: Could not find the author for "Mask-Group-29-1.jpg" (post #1691) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 1691 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 1691 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Running post-processing for post 1692 [] []
[2023-08-14 12:58:58] merlin-logger.WARNING: Could not find the author for "Image_27-1.jpeg" (post #1692) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 1692 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 1692 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Running post-processing for post 1693 [] []
[2023-08-14 12:58:58] merlin-logger.WARNING: Could not find the author for "featured_image-1.jpg" (post #1693) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 1693 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 1693 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Running post-processing for post 1694 [] []
[2023-08-14 12:58:58] merlin-logger.WARNING: Could not find the author for "Image_29-1.png" (post #1694) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 1694 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 1694 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Running post-processing for post 46828 [] []
[2023-08-14 12:58:58] merlin-logger.WARNING: Could not find the author for "photo-1475452779376-caebfb988090" (post #46828) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 46828 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 46828 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Running post-processing for post 46829 [] []
[2023-08-14 12:58:58] merlin-logger.WARNING: Could not find the author for "photo-1461749280684-dccba630e2f6" (post #46829) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 46829 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 46829 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Running post-processing for post 46868 [] []
[2023-08-14 12:58:58] merlin-logger.WARNING: Could not find the author for "msbasics" (post #46868) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 46868 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 46868 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Running post-processing for post 48291 [] []
[2023-08-14 12:58:58] merlin-logger.WARNING: Could not find the author for "logo@2x" (post #48291) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 48291 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Running post-processing for post 48299 [] []
[2023-08-14 12:58:58] merlin-logger.WARNING: Could not find the author for "staff_3-350&#215;250" (post #48299) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 48299 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Running post-processing for post 48300 [] []
[2023-08-14 12:58:58] merlin-logger.WARNING: Could not find the author for "staff_2-350&#215;250" (post #48300) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 48300 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Running post-processing for post 48301 [] []
[2023-08-14 12:58:58] merlin-logger.WARNING: Could not find the author for "staff_1-350&#215;250" (post #48301) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 48301 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Running post-processing for post 48320 [] []
[2023-08-14 12:58:58] merlin-logger.WARNING: Could not find the author for "Shape-1-1" (post #48320) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 48320 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Running post-processing for post 48321 [] []
[2023-08-14 12:58:58] merlin-logger.WARNING: Could not find the author for "Shape-1" (post #48321) [] []
[2023-08-14 12:58:58] merlin-logger.DEBUG: Post 48321 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48383 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "ms_coin_b" (post #48383) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48383 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48383 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48391 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the post parent for "NYC-Department-of-Education-School-Technology-Summit-2015-1000&#215;500-1-870&#215;440" (post #48391) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48391 was imported with parent 48390, but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "NYC-Department-of-Education-School-Technology-Summit-2015-1000&#215;500-1-870&#215;440" (post #48391) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48391 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48391 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48394 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the post parent for "photo-1491897554428-130a60dd4757-870&#215;440" (post #48394) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48394 was imported with parent 48393, but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "photo-1491897554428-130a60dd4757-870&#215;440" (post #48394) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48394 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48394 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48508 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "curtis-macnewton-12711-unsplash-min" (post #48508) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48508 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48508 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48509 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "cristian-grecu-762012-unsplash-min" (post #48509) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48509 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48509 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48510 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Default Kit" (post #48510) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48510 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48510 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 1695 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "home live" (post #1695) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 1695 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48145 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "header" (post #48145) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48145 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48145 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48148 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "footer" (post #48148) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48148 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48148 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48162 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Starter MS" (post #48162) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48162 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48162 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48511 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "jakob-owens-198234-unsplash-min-1" (post #48511) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48511 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48511 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48512 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "landscape" (post #48512) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48512 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48512 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48513 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "photo-1416339134316-0e91dc9ded92" (post #48513) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48513 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48513 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48514 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "photo-1496307042754-b4aa456c4a2d" (post #48514) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48514 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48514 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48515 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "photo-1499720843949-d9e6f318dca0-1" (post #48515) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48515 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48515 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48516 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "cathryn-lavery-67852-unsplash" (post #48516) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48516 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48536 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "photo-1512686096451-a15c19314d59" (post #48536) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48536 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48536 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48537 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "photo-1513829596324-4bb2800c5efb" (post #48537) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48537 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48537 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48541 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "photo-1525994886773-080587e161c2" (post #48541) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48541 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48541 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48868 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the post parent for "jakob-owens-198234-unsplash-min-1-1024&#215;664" (post #48868) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48868 was imported with parent 48395, but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "jakob-owens-198234-unsplash-min-1-1024&#215;664" (post #48868) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48868 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48868 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48869 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the post parent for "photo-1416339134316-0e91dc9ded92-1024&#215;767" (post #48869) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48869 was imported with parent 48393, but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "photo-1416339134316-0e91dc9ded92-1024&#215;767" (post #48869) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48869 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48869 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48870 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the post parent for "photo-1496307042754-b4aa456c4a2d-1024&#215;683" (post #48870) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48870 was imported with parent 48390, but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "photo-1496307042754-b4aa456c4a2d-1024&#215;683" (post #48870) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48870 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48870 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48871 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the post parent for "photo-1499720843949-d9e6f318dca0-1-1024&#215;683" (post #48871) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48871 was imported with parent 48385, but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "photo-1499720843949-d9e6f318dca0-1-1024&#215;683" (post #48871) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48871 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48871 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48873 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the post parent for "photo-1512686096451-a15c19314d59-1024&#215;683" (post #48873) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48873 was imported with parent 48388, but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "photo-1512686096451-a15c19314d59-1024&#215;683" (post #48873) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48873 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48873 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48923 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "woocommerce-placeholder" (post #48923) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48923 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48923 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48924 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Hello world!" (post #48924) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48924 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48924 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48925 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Checkout" (post #48925) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48925 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48925 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48926 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Courses Page" (post #48926) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48926 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48926 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48927 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "User Account" (post #48927) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48927 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48927 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48928 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "User Public Account" (post #48928) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48928 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48928 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48929 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Wishlist" (post #48929) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48929 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48929 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 1689 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Courses" (post #1689) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 1689 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 1689 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48164 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "header" (post #48164) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48164 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48164 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48166 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "footer" (post #48166) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48166 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48166 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48168 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "home live" (post #48168) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48168 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48170 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Home" (post #48170) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48170 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48178 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "footer" (post #48178) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48178 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48178 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48181 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "header" (post #48181) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48181 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48212 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Hello world!" (post #48212) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48212 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48212 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48266 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Home" (post #48266) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48266 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48266 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48269 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Add Course" (post #48269) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48269 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48269 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 49087 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "" (post #49087) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 49087 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 49087 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 868 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Minimalism, How to make things simpler" (post #868) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 868 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 868 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 869 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "How to get comfortable on camera" (post #869) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 869 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 869 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 49088 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Web Coding and Apache Basics theory" (post #49088) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 49088 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 49088 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 996 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "How to Make Beautiful Landscape photos?" (post #996) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 996 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 996 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 997 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Real Things Art Painting by Jason Ni" (post #997) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 997 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 997 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 999 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Road Bike Manual or How to Be a Champion" (post #999) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 999 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 999 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 1001 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Graphic Design Basics Masterclass" (post #1001) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 1001 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 1001 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 1002 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Fashion Photography from professional" (post #1002) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 1002 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 1002 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 1073 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "How to be a DJ? Make Electronic Music" (post #1073) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 1073 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 1073 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 47961 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Part 1 &#8211; Your First Ride" (post #47961) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 47961 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 47961 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 47962 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Part 2 &#8211; A Closer Introduction To The Necessary Tools And Knowledge" (post #47962) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 47962 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 47962 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 47963 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Part 3 &#8211; Structure Your Training" (post #47963) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 47963 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 47963 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 47964 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Part 4 &#8211; Finding New Training Grounds" (post #47964) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 47964 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 47964 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 47971 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Lecture 2: What&#8217;s Your Story?" (post #47971) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 47971 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 47971 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 47972 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Lecture 3 &#8211; The Illusion of Space Stream" (post #47972) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 47972 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 47972 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 47973 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Lecture 4 – Best Ways to Create Illusion" (post #47973) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 47973 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 47973 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 3762 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "The most famous personalities in IT?" (post #3762) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 3762 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 3762 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 3766 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "What does CPU stand for?" (post #3766) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 3766 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 3766 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 3769 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Fill the Gap" (post #3769) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 3769 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 3769 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 3770 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Which of these is not an early computer?" (post #3770) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 3770 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 3770 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 3772 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Match Actors Names" (post #3772) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 3772 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 3772 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 3773 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Type all computer parts you know" (post #3773) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 3773 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 3773 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 3774 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Is 380MBps transfer rate of a standard USB 2.0 Device?" (post #3774) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 3774 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 3774 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 3775 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Which of the indicated programming languages ​​can create mobile sentences" (post #3775) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 3775 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 3775 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 3776 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "The most famous personalities in IT?" (post #3776) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 3776 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 3776 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 3779 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "What is the most common OS in the world?" (post #3779) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 3779 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 3779 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 3780 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "What does CPU stand for?" (post #3780) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 3780 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 3780 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 47970 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Lecture 1: Ideas About Painting" (post #47970) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 47970 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 47970 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48097 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Middle Quiz" (post #48097) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48097 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48097 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48140 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Custom Styles" (post #48140) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48140 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48140 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48398 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Custom Styles" (post #48398) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48398 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48398 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Running post-processing for post 48742 [] []
[2023-08-14 12:58:59] merlin-logger.WARNING: Could not find the author for "Part 5 &#8211; Zoom Conference" (post #48742) [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48742 was imported with author "cvrox2023", but could not be found [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: Post 48742 was marked for post-processing, but none was required. [] []
[2023-08-14 12:58:59] merlin-logger.INFO: Starting remapping of featured images [] []
[2023-08-14 12:58:59] merlin-logger.INFO: Remapping featured image ID 48515 to new ID 48516 for post ID 868 [] []
[2023-08-14 12:58:59] merlin-logger.INFO: Remapping featured image ID 48512 to new ID 48513 for post ID 869 [] []
[2023-08-14 12:58:59] merlin-logger.INFO: Remapping featured image ID 48512 to new ID 48513 for post ID 1001 [] []
[2023-08-14 12:58:59] merlin-logger.INFO: Remapping featured image ID 48510 to new ID 48511 for post ID 1002 [] []
[2023-08-14 12:58:59] merlin-logger.INFO: Remapping featured image ID 48513 to new ID 48514 for post ID 1073 [] []
[2023-08-14 12:58:59] merlin-logger.DEBUG: The home page was set {"homepage_id":"[object] (WP_Post: {\"ID\":288,\"post_author\":\"1\",\"post_date\":\"2019-05-16 02:41:58\",\"post_date_gmt\":\"2019-05-16 02:41:58\",\"post_content\":\"<link href=\\\"https://fonts.googleapis.com/css?family=Nunito:700%2C300\\\" rel=\\\"stylesheet\\\" property=\\\"stylesheet\\\" type=\\\"text/css\\\" media=\\\"all\\\">\\n<!-- START REVOLUTION SLIDER 5.4.8.1 fullwidth mode -->\\n<ul>\\t<!-- SLIDE  -->\\n\\t<li data-index=\\\"rs-1\\\" data-transition=\\\"fade\\\" data-slotamount=\\\"default\\\" data-hideafterloop=\\\"0\\\" data-hideslideonmobile=\\\"off\\\"  data-easein=\\\"default\\\" data-easeout=\\\"default\\\" data-masterspeed=\\\"300\\\"  data-thumb=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/05/home1-slider-1-100x50.jpg\\\"  data-rotate=\\\"0\\\"  data-saveperformance=\\\"off\\\"  data-title=\\\"Slide\\\" data-param1=\\\"\\\" data-param2=\\\"\\\" data-param3=\\\"\\\" data-param4=\\\"\\\" data-param5=\\\"\\\" data-param6=\\\"\\\" data-param7=\\\"\\\" data-param8=\\\"\\\" data-param9=\\\"\\\" data-param10=\\\"\\\" data-description=\\\"\\\">\\n\\t\\t<!-- MAIN IMAGE -->\\n\\t\\t<img src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/05/home1-slider-1.jpg\\\"  alt=\\\"\\\" title=\\\"home1-slider-1\\\"  width=\\\"1920\\\" height=\\\"960\\\" data-bgposition=\\\"center center\\\" data-bgfit=\\\"cover\\\" data-bgrepeat=\\\"no-repeat\\\" data-no-retina>\\n\\t\\t<!-- LAYERS -->\\n\\t\\t<!-- LAYER NR. 1 -->\\n\\t\\tSelf EducatIon Resources and Infos \\n\\t\\t<!-- LAYER NR. 2 -->\\n\\t\\tTechnology is brining a massive wave of evolution on learning <br> things on different ways. \\n\\t\\t<!-- LAYER NR. 3 -->\\n\\t\\tReady to get Started? \\n\\t\\t<!-- LAYER NR. 4 -->\\n\\t\\tSelf EducatIon Resources <br> and Infos \\n\\t</li>\\n\\t<!-- SLIDE  -->\\n\\t<li data-index=\\\"rs-6\\\" data-transition=\\\"fade\\\" data-slotamount=\\\"default\\\" data-hideafterloop=\\\"0\\\" data-hideslideonmobile=\\\"off\\\"  data-easein=\\\"default\\\" data-easeout=\\\"default\\\" data-masterspeed=\\\"300\\\"  data-thumb=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/05/home1-slider-2-100x50.jpg\\\"  data-rotate=\\\"0\\\"  data-saveperformance=\\\"off\\\"  data-title=\\\"Slide\\\" data-param1=\\\"\\\" data-param2=\\\"\\\" data-param3=\\\"\\\" data-param4=\\\"\\\" data-param5=\\\"\\\" data-param6=\\\"\\\" data-param7=\\\"\\\" data-param8=\\\"\\\" data-param9=\\\"\\\" data-param10=\\\"\\\" data-description=\\\"\\\">\\n\\t\\t<!-- MAIN IMAGE -->\\n\\t\\t<img src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/05/home1-slider-2.jpg\\\"  alt=\\\"\\\" title=\\\"home1-slider-2\\\"  width=\\\"1920\\\" height=\\\"960\\\" data-bgposition=\\\"center center\\\" data-bgfit=\\\"cover\\\" data-bgrepeat=\\\"no-repeat\\\" data-no-retina>\\n\\t\\t<!-- LAYERS -->\\n\\t\\t<!-- LAYER NR. 5 -->\\n\\t\\tHow to enroll your chıld to a class? \\n\\t\\t<!-- LAYER NR. 6 -->\\n\\t\\tKnown for its academic excellence the school offers <br>departments and programs. \\n\\t\\t<!-- LAYER NR. 7 -->\\n\\t\\tReady to get Started? \\n\\t\\t<!-- LAYER NR. 8 -->\\n\\t\\tHow to enroll your chıld <br> to a class? \\n\\t</li>\\n</ul>\\n<style>\\n</style>\\t\\n<style></style>\\t\\t\\n\\t\\t\\t\\t\\t\\t<style type=\\\"text/css\\\">.arrow-theme.tparrows{cursor:pointer;background-color:transparent;position:absolute;display:block;z-index:100;width:80px;height:50px}.arrow-theme.tparrows.tp-leftarrow::before{content:\\\"f108\\\";text-align:left}.arrow-theme.tparrows::before{font-size:24px;color:#fff;display:block;line-height:50px;font-family:\\\"Flaticon\\\"}.arrow-theme.tparrows.tp-leftarrow::after{content:'prev';left:43px}.arrow-theme.tparrows::after{color:#fff;overflow:hidden;text-transform:uppercase;line-height:normal;font-weight:400;font-family:\\\"Nunito\\\",Arial,sans-serif;position:absolute;top:50%;-webkit-transform:translateY(-50%);-ms-transform:translateY(-50%);-moz-transform:translateY(-50%);transform:translateY(-50%);width:20px;height:auto}.arrow-theme.tparrows.tp-rightarrow::before{content:\\\"f107\\\";text-align:right}.arrow-theme.tparrows.tp-rightarrow::after{content:'next';right:43px}</style>\\n\\t\\t\\t\\t<!-- END REVOLUTION SLIDER -->        \\n                            <a href=\\\"#scroll-to-this\\\"></a>\\n                                    <h3>Learn From The Experts</h3>\\n                                    <h3>Book Library &amp; Store</h3>\\n                                    <h3>Worldwide Recognize</h3>\\n                                    <h3>Best Industry Leaders</h3>\\n\\t\\t\\t<h2>Via School Categories Courses</h2>Cum doctus civibus efficiantur in imperdiet deterruisset.\\t\\t\\n                                                            <h3>Design</h3>\\n                                                            Over 800 Courses\\n                            <a href=\\\"#\\\">\\n                                                            <h3>Business</h3>\\n                                                            Over 1,400 Courses\\n                            </a>\\n                            <a href=\\\"#\\\">\\n                                                            <h3>Software Development</h3>\\n                                                            Over 350 Courses\\n                            </a>\\n                            <a href=\\\"#\\\">\\n                                                            <h3>Web Development</h3>\\n                                                            Over 640 Courses\\n                            </a>\\n                            <a href=\\\"#\\\">\\n                                                            <h3>Marketing</h3>\\n                                                            Over 200 Courses\\n                            </a>\\n                            <a href=\\\"#\\\">\\n                                                            <h3>Business</h3>\\n                                                            Over 1,400 Courses\\n                            </a>\\n                            <a href=\\\"#\\\">\\n                                                            <h3>Software Development</h3>\\n                                                            Over 350 Courses\\n                            </a>\\n                            <a href=\\\"#\\\">\\n                                                            <h3>3D + Animation</h3>\\n                                                            Over 900 Courses\\n                            </a>\\n\\t\\t\\t<a href=\\\"#\\\" role=\\\"button\\\">\\n\\t\\t\\t\\t\\t\\tView All Courses\\n\\t\\t\\t\\t\\t</a>\\n\\t\\t\\t<h2>Starting online learning</h2>\\t\\t\\n\\t\\t\\t<h2>Enhance your skills with best <br> online courses</h2>\\t\\t\\n\\t\\t\\t<a href=\\\"#\\\" role=\\\"button\\\">\\n\\t\\t\\t\\t\\t\\tGet Started Now\\n\\t\\t\\t\\t\\t</a>\\n\\t\\t\\t<h2>Browse Our Top Courses</h2>Cum doctus civibus efficiantur in imperdiet deterruisset.\\t\\t\\n                        <ul role=\\\"tablist\\\">\\n                                                            <li>\\n                                    <a href=\\\"#tab-8ZFar-0\\\" data-toggle=\\\"tab\\\">\\n                                                                                    Developer                                                                            </a>\\n                                </li>\\n                                                            <li>\\n                                    <a href=\\\"#tab-8ZFar-1\\\" data-toggle=\\\"tab\\\">\\n                                                                                    Business                                                                            </a>\\n                                </li>\\n                                                            <li>\\n                                    <a href=\\\"#tab-8ZFar-2\\\" data-toggle=\\\"tab\\\">\\n                                                                                    Design                                                                            </a>\\n                                </li>\\n                                                            <li>\\n                                    <a href=\\\"#tab-8ZFar-3\\\" data-toggle=\\\"tab\\\">\\n                                                                                    Web                                                                            </a>\\n                                </li>\\n                                                            <li>\\n                                    <a href=\\\"#tab-8ZFar-4\\\" data-toggle=\\\"tab\\\">\\n                                                                                    Marketing                                                                            </a>\\n                                </li>\\n                                                    </ul>\\n        <!-- course thumbnail -->\\n                    <a href=\\\"https://demoapus2.com/edumy/courses/the-complete-shopify-aliexpress-dropship/\\\">\\n                        <img width=\\\"614\\\" height=\\\"400\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/1110-614x400.jpg\\\" alt=\\\"The Complete Shopify Aliexpress Dropship\\\" />                    </a>\\n\\t\\t\\t<a href=\\\"#apus-wishlist-add\\\" data-id=\\\"655\\\">\\n\\t\\t\\t\\tAdd to wishlist\\n\\t\\t\\t</a>\\n                        <a href=\\\"https://demoapus2.com/edumy/courses/the-complete-shopify-aliexpress-dropship/\\\">\\n                            Preview Course                        </a>\\n                <!-- course teacher -->\\n                <a href=\\\"https://demoapus2.com/edumy/profile/\\\">Afell Liberia</a>\\n                <!-- course title -->\\n                <a href=\\\"https://demoapus2.com/edumy/courses/the-complete-shopify-aliexpress-dropship/\\\">\\n                    <h3>The Complete Shopify Aliexpress Dropship</h3>\\n                </a>\\n\\t            <ul>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t            <ul  style=\\\"width: 50%\\\" >\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t        \\t            (2)\\n                    <!-- number students -->\\n                        75                    \\n                    <!-- number lessons -->\\n                        16                    \\n                <!-- price -->\\n        Free\\n        <!-- course thumbnail -->\\n                    <a href=\\\"https://demoapus2.com/edumy/courses/the-real-estate-financial-modeling-bootcamp/\\\">\\n                        <img width=\\\"614\\\" height=\\\"400\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/295879-614x400.jpg\\\" alt=\\\"The Real Estate Financial Modeling Bootcamp\\\" />                    </a>\\n\\t\\t\\t<a href=\\\"#apus-wishlist-add\\\" data-id=\\\"653\\\">\\n\\t\\t\\t\\tAdd to wishlist\\n\\t\\t\\t</a>\\n                        <a href=\\\"https://demoapus2.com/edumy/courses/the-real-estate-financial-modeling-bootcamp/\\\">\\n                            Preview Course                        </a>\\n                <!-- course teacher -->\\n                <a href=\\\"https://demoapus2.com/edumy/profile/\\\">Afell Liberia</a>\\n                <!-- course title -->\\n                <a href=\\\"https://demoapus2.com/edumy/courses/the-real-estate-financial-modeling-bootcamp/\\\">\\n                    <h3>The Real Estate Financial Modeling Bootcamp</h3>\\n                </a>\\n\\t            <ul>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t            <ul  style=\\\"width: 73.4%\\\" >\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t        \\t            (3)\\n                    <!-- number students -->\\n                        74                    \\n                    <!-- number lessons -->\\n                        10                    \\n                <!-- price -->\\n            &#36;29.00\\n        &#36;11.90\\n        <!-- course thumbnail -->\\n                    <a href=\\\"https://demoapus2.com/edumy/courses/the-complete-product-management-course/\\\">\\n                        <img width=\\\"614\\\" height=\\\"400\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/5247-614x400.jpg\\\" alt=\\\"The Complete Product Management Course\\\" />                    </a>\\n\\t\\t\\t<a href=\\\"#apus-wishlist-add\\\" data-id=\\\"651\\\">\\n\\t\\t\\t\\tAdd to wishlist\\n\\t\\t\\t</a>\\n                        <a href=\\\"https://demoapus2.com/edumy/courses/the-complete-product-management-course/\\\">\\n                            Preview Course                        </a>\\n                <!-- course teacher -->\\n                <a href=\\\"https://demoapus2.com/edumy/profile/Krisztina+Szer/\\\">Krisztina Szer</a>\\n                <!-- course title -->\\n                <a href=\\\"https://demoapus2.com/edumy/courses/the-complete-product-management-course/\\\">\\n                    <h3>The Complete Product Management Course</h3>\\n                </a>\\n\\t            <ul>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t            <ul  style=\\\"width: 80%\\\" >\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t        \\t            (1)\\n                    <!-- number students -->\\n                        74                    \\n                    <!-- number lessons -->\\n                        14                    \\n                <!-- price -->\\n            &#36;29.00\\n        &#36;11.90\\n        <!-- course thumbnail -->\\n                    <a href=\\\"https://demoapus2.com/edumy/courses/sales-training-practical-sales-techniques/\\\">\\n                        <img width=\\\"600\\\" height=\\\"400\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/990.jpg\\\" alt=\\\"Sales Training Practical Sales Techniques\\\" />                    </a>\\n\\t\\t\\t<a href=\\\"#apus-wishlist-add\\\" data-id=\\\"649\\\">\\n\\t\\t\\t\\tAdd to wishlist\\n\\t\\t\\t</a>\\n                        <a href=\\\"https://demoapus2.com/edumy/courses/sales-training-practical-sales-techniques/\\\">\\n                            Preview Course                        </a>\\n                <!-- course teacher -->\\n                <a href=\\\"https://demoapus2.com/edumy/profile/John+Wick/\\\">John Wick</a>\\n                <!-- course title -->\\n                <a href=\\\"https://demoapus2.com/edumy/courses/sales-training-practical-sales-techniques/\\\">\\n                    <h3>Sales Training Practical Sales Techniques</h3>\\n                </a>\\n\\t            <ul>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t            <ul  style=\\\"width: 40%\\\" >\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t        \\t            (1)\\n                    <!-- number students -->\\n                        74                    \\n                    <!-- number lessons -->\\n                        8                    \\n                <!-- price -->\\n            &#36;34.00\\n        &#36;23.00\\n        <!-- course thumbnail -->\\n                    <a href=\\\"https://demoapus2.com/edumy/courses/learn-ethical-hacking-from-scratch/\\\">\\n                        <img width=\\\"614\\\" height=\\\"400\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/05/1157-614x400.jpg\\\" alt=\\\"Learn Ethical Hacking from Scratch Your Stepping\\\" />                    </a>\\n\\t\\t\\t<a href=\\\"#apus-wishlist-add\\\" data-id=\\\"647\\\">\\n\\t\\t\\t\\tAdd to wishlist\\n\\t\\t\\t</a>\\n                        <a href=\\\"https://demoapus2.com/edumy/courses/learn-ethical-hacking-from-scratch/\\\">\\n                            Preview Course                        </a>\\n                <!-- course teacher -->\\n                <a href=\\\"https://demoapus2.com/edumy/profile/Chris+Park/\\\">Chris Park</a>\\n                <!-- course title -->\\n                <a href=\\\"https://demoapus2.com/edumy/courses/learn-ethical-hacking-from-scratch/\\\">\\n                    <h3>Learn Ethical Hacking from Scratch Your Stepping</h3>\\n                </a>\\n\\t            <ul>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t            <ul  style=\\\"width: 60%\\\" >\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t        \\t            (1)\\n                    <!-- number students -->\\n                        74                    \\n                    <!-- number lessons -->\\n                        12                    \\n                <!-- price -->\\n        Free\\n        <!-- course thumbnail -->\\n                    <a href=\\\"https://demoapus2.com/edumy/courses/the-complete-salesforce-classic-administrator/\\\">\\n                        <img width=\\\"614\\\" height=\\\"400\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/5247-614x400.jpg\\\" alt=\\\"The Complete Salesforce Classic Administrator\\\" />                    </a>\\n\\t\\t\\t<a href=\\\"#apus-wishlist-add\\\" data-id=\\\"644\\\">\\n\\t\\t\\t\\tAdd to wishlist\\n\\t\\t\\t</a>\\n                        <a href=\\\"https://demoapus2.com/edumy/courses/the-complete-salesforce-classic-administrator/\\\">\\n                            Preview Course                        </a>\\n                <!-- course teacher -->\\n                <a href=\\\"https://demoapus2.com/edumy/profile/Kate+Hudson/\\\">Kate Hudson</a>\\n                <!-- course title -->\\n                <a href=\\\"https://demoapus2.com/edumy/courses/the-complete-salesforce-classic-administrator/\\\">\\n                    <h3>The Complete Salesforce Classic Administrator</h3>\\n                </a>\\n\\t            <ul>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t            <ul  style=\\\"width: 90%\\\" >\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t        \\t            (2)\\n                    <!-- number students -->\\n                        74                    \\n                    <!-- number lessons -->\\n                        17                    \\n                <!-- price -->\\n            &#36;67.00\\n        &#36;58.00\\n        <!-- course thumbnail -->\\n                    <a href=\\\"https://demoapus2.com/edumy/courses/microsoft-sql-server-2017-for-everyone/\\\">\\n                        <img width=\\\"614\\\" height=\\\"400\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/217-614x400.jpg\\\" alt=\\\"Microsoft SQL Server 2017 for Everyone\\\" />                    </a>\\n\\t\\t\\t<a href=\\\"#apus-wishlist-add\\\" data-id=\\\"642\\\">\\n\\t\\t\\t\\tAdd to wishlist\\n\\t\\t\\t</a>\\n                        <a href=\\\"https://demoapus2.com/edumy/courses/microsoft-sql-server-2017-for-everyone/\\\">\\n                            Preview Course                        </a>\\n                <!-- course teacher -->\\n                <a href=\\\"https://demoapus2.com/edumy/profile/Peter+Jackson/\\\">Peter Jackson</a>\\n                <!-- course title -->\\n                <a href=\\\"https://demoapus2.com/edumy/courses/microsoft-sql-server-2017-for-everyone/\\\">\\n                    <h3>Microsoft SQL Server 2017 for Everyone</h3>\\n                </a>\\n\\t            <ul>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t            <ul  style=\\\"width: 60%\\\" >\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t        \\t            (1)\\n                    <!-- number students -->\\n                        74                    \\n                    <!-- number lessons -->\\n                        7                    \\n                <!-- price -->\\n            &#36;29.00\\n        &#36;23.00\\n        <!-- course thumbnail -->\\n                    <a href=\\\"https://demoapus2.com/edumy/courses/learn-react-js-and-web-api-by-creating-a-full-stack/\\\">\\n                        <img width=\\\"614\\\" height=\\\"400\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/1078-614x400.jpg\\\" alt=\\\"Learn React JS and Web API by creating a Full Stack\\\" />                    </a>\\n\\t\\t\\t<a href=\\\"#apus-wishlist-add\\\" data-id=\\\"640\\\">\\n\\t\\t\\t\\tAdd to wishlist\\n\\t\\t\\t</a>\\n                        <a href=\\\"https://demoapus2.com/edumy/courses/learn-react-js-and-web-api-by-creating-a-full-stack/\\\">\\n                            Preview Course                        </a>\\n                <!-- course teacher -->\\n                <a href=\\\"https://demoapus2.com/edumy/profile/Maison+Le+Lou/\\\">Maison Le Lou</a>\\n                <!-- course title -->\\n                <a href=\\\"https://demoapus2.com/edumy/courses/learn-react-js-and-web-api-by-creating-a-full-stack/\\\">\\n                    <h3>Learn React JS and Web API by creating a Full Stack</h3>\\n                </a>\\n\\t            <ul>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t            <ul  style=\\\"width: 95%\\\" >\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t        \\t            (4)\\n                    <!-- number students -->\\n                        23                    \\n                    <!-- number lessons -->\\n                        9                    \\n                <!-- price -->\\n            &#36;85.00\\n        &#36;44.00\\n        <!-- course thumbnail -->\\n                    <a href=\\\"https://demoapus2.com/edumy/courses/the-complete-shopify-aliexpress-dropship/\\\">\\n                        <img width=\\\"614\\\" height=\\\"400\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/1110-614x400.jpg\\\" alt=\\\"The Complete Shopify Aliexpress Dropship\\\" />                    </a>\\n\\t\\t\\t<a href=\\\"#apus-wishlist-add\\\" data-id=\\\"655\\\">\\n\\t\\t\\t\\tAdd to wishlist\\n\\t\\t\\t</a>\\n                        <a href=\\\"https://demoapus2.com/edumy/courses/the-complete-shopify-aliexpress-dropship/\\\">\\n                            Preview Course                        </a>\\n                <!-- course teacher -->\\n                <a href=\\\"https://demoapus2.com/edumy/profile/\\\">Afell Liberia</a>\\n                <!-- course title -->\\n                <a href=\\\"https://demoapus2.com/edumy/courses/the-complete-shopify-aliexpress-dropship/\\\">\\n                    <h3>The Complete Shopify Aliexpress Dropship</h3>\\n                </a>\\n\\t            <ul>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t            <ul  style=\\\"width: 50%\\\" >\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t        \\t            (2)\\n                    <!-- number students -->\\n                        75                    \\n                    <!-- number lessons -->\\n                        16                    \\n                <!-- price -->\\n        Free\\n        <!-- course thumbnail -->\\n                    <a href=\\\"https://demoapus2.com/edumy/courses/the-real-estate-financial-modeling-bootcamp/\\\">\\n                        <img width=\\\"614\\\" height=\\\"400\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/295879-614x400.jpg\\\" alt=\\\"The Real Estate Financial Modeling Bootcamp\\\" />                    </a>\\n\\t\\t\\t<a href=\\\"#apus-wishlist-add\\\" data-id=\\\"653\\\">\\n\\t\\t\\t\\tAdd to wishlist\\n\\t\\t\\t</a>\\n                        <a href=\\\"https://demoapus2.com/edumy/courses/the-real-estate-financial-modeling-bootcamp/\\\">\\n                            Preview Course                        </a>\\n                <!-- course teacher -->\\n                <a href=\\\"https://demoapus2.com/edumy/profile/\\\">Afell Liberia</a>\\n                <!-- course title -->\\n                <a href=\\\"https://demoapus2.com/edumy/courses/the-real-estate-financial-modeling-bootcamp/\\\">\\n                    <h3>The Real Estate Financial Modeling Bootcamp</h3>\\n                </a>\\n\\t            <ul>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t            <ul  style=\\\"width: 73.4%\\\" >\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t        \\t            (3)\\n                    <!-- number students -->\\n                        74                    \\n                    <!-- number lessons -->\\n                        10                    \\n                <!-- price -->\\n            &#36;29.00\\n        &#36;11.90\\n        <!-- course thumbnail -->\\n                    <a href=\\\"https://demoapus2.com/edumy/courses/aws-certified-cloud-practitioner-2019/\\\">\\n                        <img width=\\\"614\\\" height=\\\"400\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/2629-614x400.jpg\\\" alt=\\\"AWS Certified Cloud Practitioner 2019\\\" />                    </a>\\n\\t\\t\\t<a href=\\\"#apus-wishlist-add\\\" data-id=\\\"618\\\">\\n\\t\\t\\t\\tAdd to wishlist\\n\\t\\t\\t</a>\\n                        <a href=\\\"https://demoapus2.com/edumy/courses/aws-certified-cloud-practitioner-2019/\\\">\\n                            Preview Course                        </a>\\n                <!-- course teacher -->\\n                <a href=\\\"https://demoapus2.com/edumy/profile/Manage+Study/\\\">Persona Humana</a>\\n                <!-- course title -->\\n                <a href=\\\"https://demoapus2.com/edumy/courses/aws-certified-cloud-practitioner-2019/\\\">\\n                    <h3>AWS Certified Cloud Practitioner 2019</h3>\\n                </a>\\n\\t            <ul>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t            <ul  style=\\\"width: 20%\\\" >\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t        \\t            (1)\\n                    <!-- number students -->\\n                        86                    \\n                    <!-- number lessons -->\\n                        17                    \\n                <!-- price -->\\n            &#36;89.00\\n        &#36;70.00\\n        <!-- course thumbnail -->\\n                    <a href=\\\"https://demoapus2.com/edumy/courses/sales-training-practical-sales-techniques/\\\">\\n                        <img width=\\\"600\\\" height=\\\"400\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/990.jpg\\\" alt=\\\"Sales Training Practical Sales Techniques\\\" />                    </a>\\n\\t\\t\\t<a href=\\\"#apus-wishlist-add\\\" data-id=\\\"649\\\">\\n\\t\\t\\t\\tAdd to wishlist\\n\\t\\t\\t</a>\\n                        <a href=\\\"https://demoapus2.com/edumy/courses/sales-training-practical-sales-techniques/\\\">\\n                            Preview Course                        </a>\\n                <!-- course teacher -->\\n                <a href=\\\"https://demoapus2.com/edumy/profile/John+Wick/\\\">John Wick</a>\\n                <!-- course title -->\\n                <a href=\\\"https://demoapus2.com/edumy/courses/sales-training-practical-sales-techniques/\\\">\\n                    <h3>Sales Training Practical Sales Techniques</h3>\\n                </a>\\n\\t            <ul>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t            <ul  style=\\\"width: 40%\\\" >\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t        \\t            (1)\\n                    <!-- number students -->\\n                        74                    \\n                    <!-- number lessons -->\\n                        8                    \\n                <!-- price -->\\n            &#36;34.00\\n        &#36;23.00\\n        <!-- course thumbnail -->\\n                    <a href=\\\"https://demoapus2.com/edumy/courses/learn-ethical-hacking-from-scratch/\\\">\\n                        <img width=\\\"614\\\" height=\\\"400\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/05/1157-614x400.jpg\\\" alt=\\\"Learn Ethical Hacking from Scratch Your Stepping\\\" />                    </a>\\n\\t\\t\\t<a href=\\\"#apus-wishlist-add\\\" data-id=\\\"647\\\">\\n\\t\\t\\t\\tAdd to wishlist\\n\\t\\t\\t</a>\\n                        <a href=\\\"https://demoapus2.com/edumy/courses/learn-ethical-hacking-from-scratch/\\\">\\n                            Preview Course                        </a>\\n                <!-- course teacher -->\\n                <a href=\\\"https://demoapus2.com/edumy/profile/Chris+Park/\\\">Chris Park</a>\\n                <!-- course title -->\\n                <a href=\\\"https://demoapus2.com/edumy/courses/learn-ethical-hacking-from-scratch/\\\">\\n                    <h3>Learn Ethical Hacking from Scratch Your Stepping</h3>\\n                </a>\\n\\t            <ul>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t            <ul  style=\\\"width: 60%\\\" >\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t        \\t            (1)\\n                    <!-- number students -->\\n                        74                    \\n                    <!-- number lessons -->\\n                        12                    \\n                <!-- price -->\\n        Free\\n        <!-- course thumbnail -->\\n                    <a href=\\\"https://demoapus2.com/edumy/courses/home-business-the-complete-cpa/\\\">\\n                        <img width=\\\"614\\\" height=\\\"400\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/6226-614x400.jpg\\\" alt=\\\"Home Business The Complete CPA\\\" />                    </a>\\n\\t\\t\\t<a href=\\\"#apus-wishlist-add\\\" data-id=\\\"620\\\">\\n\\t\\t\\t\\tAdd to wishlist\\n\\t\\t\\t</a>\\n                        <a href=\\\"https://demoapus2.com/edumy/courses/home-business-the-complete-cpa/\\\">\\n                            Preview Course                        </a>\\n                <!-- course teacher -->\\n                <a href=\\\"https://demoapus2.com/edumy/profile/Afell+Liberia/\\\">Wilanów Aseco</a>\\n                <!-- course title -->\\n                <a href=\\\"https://demoapus2.com/edumy/courses/home-business-the-complete-cpa/\\\">\\n                    <h3>Home Business The Complete CPA</h3>\\n                </a>\\n\\t            <ul>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t            <ul  style=\\\"width: 60%\\\" >\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t        \\t            (1)\\n                    <!-- number students -->\\n                        74                    \\n                    <!-- number lessons -->\\n                        4                    \\n                <!-- price -->\\n            &#36;29.00\\n        &#36;11.90\\n        <!-- course thumbnail -->\\n                    <a href=\\\"https://demoapus2.com/edumy/courses/introduction-web-design-with-html/\\\">\\n                        <img width=\\\"614\\\" height=\\\"400\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/78095-614x400.jpg\\\" alt=\\\"Introduction Web Design with HTML\\\" />                    </a>\\n\\t\\t\\t<a href=\\\"#apus-wishlist-add\\\" data-id=\\\"64\\\">\\n\\t\\t\\t\\tAdd to wishlist\\n\\t\\t\\t</a>\\n                        <a href=\\\"https://demoapus2.com/edumy/courses/introduction-web-design-with-html/\\\">\\n                            Preview Course                        </a>\\n                <!-- course teacher -->\\n                <a href=\\\"https://demoapus2.com/edumy/profile/Andrew+Williams/\\\">Andrew Williams</a>\\n                <!-- course title -->\\n                <a href=\\\"https://demoapus2.com/edumy/courses/introduction-web-design-with-html/\\\">\\n                    <h3>Introduction Web Design with HTML</h3>\\n                </a>\\n\\t            <ul>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t            <ul  style=\\\"width: 80%\\\" >\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t        \\t            (3)\\n                    <!-- number students -->\\n                        86                    \\n                    <!-- number lessons -->\\n                        23                    \\n                <!-- price -->\\n            &#36;59.00\\n        &#36;49.00\\n        <!-- course thumbnail -->\\n                    <a href=\\\"https://demoapus2.com/edumy/courses/the-complete-product-management-course/\\\">\\n                        <img width=\\\"614\\\" height=\\\"400\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/5247-614x400.jpg\\\" alt=\\\"The Complete Product Management Course\\\" />                    </a>\\n\\t\\t\\t<a href=\\\"#apus-wishlist-add\\\" data-id=\\\"651\\\">\\n\\t\\t\\t\\tAdd to wishlist\\n\\t\\t\\t</a>\\n                        <a href=\\\"https://demoapus2.com/edumy/courses/the-complete-product-management-course/\\\">\\n                            Preview Course                        </a>\\n                <!-- course teacher -->\\n                <a href=\\\"https://demoapus2.com/edumy/profile/Krisztina+Szer/\\\">Krisztina Szer</a>\\n                <!-- course title -->\\n                <a href=\\\"https://demoapus2.com/edumy/courses/the-complete-product-management-course/\\\">\\n                    <h3>The Complete Product Management Course</h3>\\n                </a>\\n\\t            <ul>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t            <ul  style=\\\"width: 80%\\\" >\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t        \\t            (1)\\n                    <!-- number students -->\\n                        74                    \\n                    <!-- number lessons -->\\n                        14                    \\n                <!-- price -->\\n            &#36;29.00\\n        &#36;11.90\\n        <!-- course thumbnail -->\\n                    <a href=\\\"https://demoapus2.com/edumy/courses/microsoft-sql-server-2017-for-everyone/\\\">\\n                        <img width=\\\"614\\\" height=\\\"400\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/217-614x400.jpg\\\" alt=\\\"Microsoft SQL Server 2017 for Everyone\\\" />                    </a>\\n\\t\\t\\t<a href=\\\"#apus-wishlist-add\\\" data-id=\\\"642\\\">\\n\\t\\t\\t\\tAdd to wishlist\\n\\t\\t\\t</a>\\n                        <a href=\\\"https://demoapus2.com/edumy/courses/microsoft-sql-server-2017-for-everyone/\\\">\\n                            Preview Course                        </a>\\n                <!-- course teacher -->\\n                <a href=\\\"https://demoapus2.com/edumy/profile/Peter+Jackson/\\\">Peter Jackson</a>\\n                <!-- course title -->\\n                <a href=\\\"https://demoapus2.com/edumy/courses/microsoft-sql-server-2017-for-everyone/\\\">\\n                    <h3>Microsoft SQL Server 2017 for Everyone</h3>\\n                </a>\\n\\t            <ul>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t            <ul  style=\\\"width: 60%\\\" >\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t        \\t            (1)\\n                    <!-- number students -->\\n                        74                    \\n                    <!-- number lessons -->\\n                        7                    \\n                <!-- price -->\\n            &#36;29.00\\n        &#36;23.00\\n        <!-- course thumbnail -->\\n                    <a href=\\\"https://demoapus2.com/edumy/courses/aws-certified-developer-associate-2019/\\\">\\n                        <img width=\\\"614\\\" height=\\\"400\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/10398-614x400.jpg\\\" alt=\\\"AWS Certified Developer &#8211; Associate 2019\\\" />                    </a>\\n\\t\\t\\t<a href=\\\"#apus-wishlist-add\\\" data-id=\\\"636\\\">\\n\\t\\t\\t\\tAdd to wishlist\\n\\t\\t\\t</a>\\n                        <a href=\\\"https://demoapus2.com/edumy/courses/aws-certified-developer-associate-2019/\\\">\\n                            Preview Course                        </a>\\n                <!-- course teacher -->\\n                <a href=\\\"https://demoapus2.com/edumy/profile/Peter+Jackson/\\\">Peter Jackson</a>\\n                <!-- course title -->\\n                <a href=\\\"https://demoapus2.com/edumy/courses/aws-certified-developer-associate-2019/\\\">\\n                    <h3>AWS Certified Developer &#8211; Associate 2019</h3>\\n                </a>\\n\\t            <ul>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t            <ul  style=\\\"width: 90%\\\" >\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t        \\t            (2)\\n                    <!-- number students -->\\n                        86                    \\n                    <!-- number lessons -->\\n                        9                    \\n                <!-- price -->\\n            &#36;59.00\\n        &#36;49.00\\n        <!-- course thumbnail -->\\n                    <a href=\\\"https://demoapus2.com/edumy/courses/the-real-estate-financial-modeling-bootcamp/\\\">\\n                        <img width=\\\"614\\\" height=\\\"400\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/295879-614x400.jpg\\\" alt=\\\"The Real Estate Financial Modeling Bootcamp\\\" />                    </a>\\n\\t\\t\\t<a href=\\\"#apus-wishlist-add\\\" data-id=\\\"653\\\">\\n\\t\\t\\t\\tAdd to wishlist\\n\\t\\t\\t</a>\\n                        <a href=\\\"https://demoapus2.com/edumy/courses/the-real-estate-financial-modeling-bootcamp/\\\">\\n                            Preview Course                        </a>\\n                <!-- course teacher -->\\n                <a href=\\\"https://demoapus2.com/edumy/profile/\\\">Afell Liberia</a>\\n                <!-- course title -->\\n                <a href=\\\"https://demoapus2.com/edumy/courses/the-real-estate-financial-modeling-bootcamp/\\\">\\n                    <h3>The Real Estate Financial Modeling Bootcamp</h3>\\n                </a>\\n\\t            <ul>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t            <ul  style=\\\"width: 73.4%\\\" >\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t        \\t            (3)\\n                    <!-- number students -->\\n                        74                    \\n                    <!-- number lessons -->\\n                        10                    \\n                <!-- price -->\\n            &#36;29.00\\n        &#36;11.90\\n        <!-- course thumbnail -->\\n                    <a href=\\\"https://demoapus2.com/edumy/courses/the-complete-salesforce-classic-administrator/\\\">\\n                        <img width=\\\"614\\\" height=\\\"400\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/5247-614x400.jpg\\\" alt=\\\"The Complete Salesforce Classic Administrator\\\" />                    </a>\\n\\t\\t\\t<a href=\\\"#apus-wishlist-add\\\" data-id=\\\"644\\\">\\n\\t\\t\\t\\tAdd to wishlist\\n\\t\\t\\t</a>\\n                        <a href=\\\"https://demoapus2.com/edumy/courses/the-complete-salesforce-classic-administrator/\\\">\\n                            Preview Course                        </a>\\n                <!-- course teacher -->\\n                <a href=\\\"https://demoapus2.com/edumy/profile/Kate+Hudson/\\\">Kate Hudson</a>\\n                <!-- course title -->\\n                <a href=\\\"https://demoapus2.com/edumy/courses/the-complete-salesforce-classic-administrator/\\\">\\n                    <h3>The Complete Salesforce Classic Administrator</h3>\\n                </a>\\n\\t            <ul>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t            <ul  style=\\\"width: 90%\\\" >\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t        \\t            (2)\\n                    <!-- number students -->\\n                        74                    \\n                    <!-- number lessons -->\\n                        17                    \\n                <!-- price -->\\n            &#36;67.00\\n        &#36;58.00\\n        <!-- course thumbnail -->\\n                    <a href=\\\"https://demoapus2.com/edumy/courses/microsoft-sql-server-2017-for-everyone/\\\">\\n                        <img width=\\\"614\\\" height=\\\"400\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/217-614x400.jpg\\\" alt=\\\"Microsoft SQL Server 2017 for Everyone\\\" />                    </a>\\n\\t\\t\\t<a href=\\\"#apus-wishlist-add\\\" data-id=\\\"642\\\">\\n\\t\\t\\t\\tAdd to wishlist\\n\\t\\t\\t</a>\\n                        <a href=\\\"https://demoapus2.com/edumy/courses/microsoft-sql-server-2017-for-everyone/\\\">\\n                            Preview Course                        </a>\\n                <!-- course teacher -->\\n                <a href=\\\"https://demoapus2.com/edumy/profile/Peter+Jackson/\\\">Peter Jackson</a>\\n                <!-- course title -->\\n                <a href=\\\"https://demoapus2.com/edumy/courses/microsoft-sql-server-2017-for-everyone/\\\">\\n                    <h3>Microsoft SQL Server 2017 for Everyone</h3>\\n                </a>\\n\\t            <ul>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t            <ul  style=\\\"width: 60%\\\" >\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t        \\t            (1)\\n                    <!-- number students -->\\n                        74                    \\n                    <!-- number lessons -->\\n                        7                    \\n                <!-- price -->\\n            &#36;29.00\\n        &#36;23.00\\n        <!-- course thumbnail -->\\n                    <a href=\\\"https://demoapus2.com/edumy/courses/learn-react-js-and-web-api-by-creating-a-full-stack/\\\">\\n                        <img width=\\\"614\\\" height=\\\"400\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/1078-614x400.jpg\\\" alt=\\\"Learn React JS and Web API by creating a Full Stack\\\" />                    </a>\\n\\t\\t\\t<a href=\\\"#apus-wishlist-add\\\" data-id=\\\"640\\\">\\n\\t\\t\\t\\tAdd to wishlist\\n\\t\\t\\t</a>\\n                        <a href=\\\"https://demoapus2.com/edumy/courses/learn-react-js-and-web-api-by-creating-a-full-stack/\\\">\\n                            Preview Course                        </a>\\n                <!-- course teacher -->\\n                <a href=\\\"https://demoapus2.com/edumy/profile/Maison+Le+Lou/\\\">Maison Le Lou</a>\\n                <!-- course title -->\\n                <a href=\\\"https://demoapus2.com/edumy/courses/learn-react-js-and-web-api-by-creating-a-full-stack/\\\">\\n                    <h3>Learn React JS and Web API by creating a Full Stack</h3>\\n                </a>\\n\\t            <ul>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t            <ul  style=\\\"width: 95%\\\" >\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t        \\t            (4)\\n                    <!-- number students -->\\n                        23                    \\n                    <!-- number lessons -->\\n                        9                    \\n                <!-- price -->\\n            &#36;85.00\\n        &#36;44.00\\n        <!-- course thumbnail -->\\n                    <a href=\\\"https://demoapus2.com/edumy/courses/aws-certified-developer-associate-2019/\\\">\\n                        <img width=\\\"614\\\" height=\\\"400\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/10398-614x400.jpg\\\" alt=\\\"AWS Certified Developer &#8211; Associate 2019\\\" />                    </a>\\n\\t\\t\\t<a href=\\\"#apus-wishlist-add\\\" data-id=\\\"636\\\">\\n\\t\\t\\t\\tAdd to wishlist\\n\\t\\t\\t</a>\\n                        <a href=\\\"https://demoapus2.com/edumy/courses/aws-certified-developer-associate-2019/\\\">\\n                            Preview Course                        </a>\\n                <!-- course teacher -->\\n                <a href=\\\"https://demoapus2.com/edumy/profile/Peter+Jackson/\\\">Peter Jackson</a>\\n                <!-- course title -->\\n                <a href=\\\"https://demoapus2.com/edumy/courses/aws-certified-developer-associate-2019/\\\">\\n                    <h3>AWS Certified Developer &#8211; Associate 2019</h3>\\n                </a>\\n\\t            <ul>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t            <ul  style=\\\"width: 90%\\\" >\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t        \\t            (2)\\n                    <!-- number students -->\\n                        86                    \\n                    <!-- number lessons -->\\n                        9                    \\n                <!-- price -->\\n            &#36;59.00\\n        &#36;49.00\\n        <!-- course thumbnail -->\\n                    <a href=\\\"https://demoapus2.com/edumy/courses/the-complete-cyber-security-course/\\\">\\n                        <img width=\\\"614\\\" height=\\\"400\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/300974-614x400.jpg\\\" alt=\\\"The Complete Cyber Security Course\\\" />                    </a>\\n\\t\\t\\t<a href=\\\"#apus-wishlist-add\\\" data-id=\\\"619\\\">\\n\\t\\t\\t\\tAdd to wishlist\\n\\t\\t\\t</a>\\n                        <a href=\\\"https://demoapus2.com/edumy/courses/the-complete-cyber-security-course/\\\">\\n                            Preview Course                        </a>\\n                <!-- course teacher -->\\n                <a href=\\\"https://demoapus2.com/edumy/profile/David+Bond/\\\">David Bond</a>\\n                <!-- course title -->\\n                <a href=\\\"https://demoapus2.com/edumy/courses/the-complete-cyber-security-course/\\\">\\n                    <h3>The Complete Cyber Security Course</h3>\\n                </a>\\n\\t            <ul>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t            <ul  style=\\\"width: 73.4%\\\" >\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t                <li></li>\\n\\t            </ul>\\n\\t        \\t            (3)\\n                    <!-- number students -->\\n                        86                    \\n                    <!-- number lessons -->\\n                        16                    \\n                <!-- price -->\\n            &#36;59.00\\n        &#36;49.00\\n\\t\\t\\t<h2>What People Say</h2>Cum doctus civibus efficiantur in imperdiet deterruisset.\\t\\t\\n                    “                    \\n                                    <img src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/avata-1.jpg\\\" alt=\\\"John Doe\\\">\\n                                    <img src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/avata-2.jpg\\\" alt=\\\"Hana Sushi\\\">\\n                                    <img src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/avata-3.jpg\\\" alt=\\\"Kate Bosworth\\\">\\n                                    <img src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/05/avatar-event-1.jpg\\\" alt=\\\"kathryn aragon\\\">\\n                                    <img src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/05/avatar-event-2.jpg\\\" alt=\\\"Confetta\\\">\\n                                    <img src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/05/avatar-event-3.jpg\\\" alt=\\\"Cornelia James\\\">\\n                                    <h3><a href=\\\"#\\\" target=\\\"_self\\\" rel=\\\"noopener noreferrer\\\">John Doe</a></h3>                                    \\n                                                                            <p>Designer</p>\\n                                                                            Customization is very easy with this theme. Clean and quality design and full\\nsupport for any kind of request! Great theme!\\n                                    <h3>Hana Sushi</h3>                                    \\n                                                                            <p>Web developer</p>\\n                                                                            Customization is very easy with this theme. Clean and quality design and full\\nsupport for any kind of request! Great theme!\\n                                    <h3><a href=\\\"#\\\" target=\\\"_self\\\" rel=\\\"noopener noreferrer\\\">Kate Bosworth</a></h3>                                    \\n                                                                            <p>Management</p>\\n                                                                            Customization is very easy with this theme. Clean and quality design and full\\nsupport for any kind of request! Great theme!\\n                                    <h3><a href=\\\"#\\\" target=\\\"_self\\\" rel=\\\"noopener noreferrer\\\">kathryn aragon</a></h3>                                    \\n                                                                            <p>Management</p>\\n                                                                            Customization is very easy with this theme. Clean and quality design and full\\nsupport for any kind of request! Great theme!\\n                                    <h3><a href=\\\"#\\\" target=\\\"_self\\\" rel=\\\"noopener noreferrer\\\">Confetta</a></h3>                                    \\n                                                                            <p>Management</p>\\n                                                                            Customization is very easy with this theme. Clean and quality design and full\\nsupport for any kind of request! Great theme!\\n                                    <h3><a href=\\\"#\\\" target=\\\"_self\\\" rel=\\\"noopener noreferrer\\\">Cornelia James</a></h3>                                    \\n                                                                            <p>Management</p>\\n                                                                            Customization is very easy with this theme. Clean and quality design and full\\nsupport for any kind of request! Great theme!\\n\\t\\t\\t<h2>Latest News And Events</h2>Cum doctus civibus efficiantur in imperdiet deterruisset.\\t\\t\\n                        <article id=\\\"post-3287\\\">\\n\\t\\t\\t\\t\\t<a href=\\\"https://demoapus2.com/edumy/simple-event/successful-self-taping/\\\" aria-hidden=\\\"true\\\">\\n\\t\\t\\t\\t<figure><img width=\\\"800\\\" height=\\\"558\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/elementor/thumbs/6927-oaw6c53lgwgapwr1moj8jbyfxeoqqfn78tj5p6sgr0.jpg\\\" alt=\\\"\\\" /></figure>\\t\\t\\t</a>\\n\\t\\t\\t\\t24\\n\\t\\t\\t\\tMay\\n\\t\\t\\t\\t\\t\\t\\t 8:00 am - 5:00 pm\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t Canada\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t<h3><a href=\\\"https://demoapus2.com/edumy/simple-event/successful-self-taping/\\\" rel=\\\"bookmark\\\">Successful Self Taping</a></h3>\\t\\t\\n</article><article id=\\\"post-3286\\\">\\n\\t\\t\\t\\t\\t<a href=\\\"https://demoapus2.com/edumy/simple-event/streaming-and-dreaming/\\\" aria-hidden=\\\"true\\\">\\n\\t\\t\\t\\t<figure><img width=\\\"800\\\" height=\\\"558\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/elementor/thumbs/1093-oaw6c53lgwgapwr1moj8jbyfxeoqqfn78tj5p6sgr0.jpg\\\" alt=\\\"\\\" /></figure>\\t\\t\\t</a>\\n\\t\\t\\t\\t20\\n\\t\\t\\t\\tMay\\n\\t\\t\\t\\t\\t\\t\\t 8:00 am - 5:00 pm\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t MA 02138, USA\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t<h3><a href=\\\"https://demoapus2.com/edumy/simple-event/streaming-and-dreaming/\\\" rel=\\\"bookmark\\\">Streaming and Dreaming</a></h3>\\t\\t\\n</article><article id=\\\"post-3283\\\">\\n\\t\\t\\t\\t\\t<a href=\\\"https://demoapus2.com/edumy/simple-event/emmy-submission-101/\\\" aria-hidden=\\\"true\\\">\\n\\t\\t\\t\\t<figure><img width=\\\"800\\\" height=\\\"558\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/elementor/thumbs/2629-oaw6c53lgwgapwr1moj8jbyfxeoqqfn78tj5p6sgr0.jpg\\\" alt=\\\"\\\" /></figure>\\t\\t\\t</a>\\n\\t\\t\\t\\t24\\n\\t\\t\\t\\tMay\\n\\t\\t\\t\\t\\t\\t\\t 8:00 am - 5:00 pm\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\t Vancouver, Canada\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t<h3><a href=\\\"https://demoapus2.com/edumy/simple-event/emmy-submission-101/\\\" rel=\\\"bookmark\\\">Emmy Submission 101</a></h3>\\t\\t\\n</article>\\n                                    <article>\\n    <figure><img width=\\\"600\\\" height=\\\"878\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/elementor/thumbs/1105-oaw6c53j16ydw6lvcfxoo4lruq7c1jhwzcxjxrrvpo.jpg\\\" alt=\\\"\\\" /></figure>            \\n                22 \\n        May  \\n            <a href=\\\"https://demoapus2.com/edumy/category/science/\\\">Science</a>        \\n                    <h4>\\n                <a href=\\\"https://demoapus2.com/edumy/mba-essentials-for-professionals/\\\">MBA Essentials for Professionals</a>\\n            </h4>\\n</article>                                \\n                                    <article>\\n    <figure><img width=\\\"600\\\" height=\\\"878\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/elementor/thumbs/301242-oaw6c53j16ydw6lvcfxoo4lruq7c1jhwzcxjxrrvpo.jpg\\\" alt=\\\"\\\" /></figure>            \\n                22 \\n        May  \\n            <a href=\\\"https://demoapus2.com/edumy/category/engineering/\\\">Engineering</a>        \\n                    <h4>\\n                <a href=\\\"https://demoapus2.com/edumy/business-fundamentals/\\\">Business Fundamentals</a>\\n            </h4>\\n</article>                                \\n\\t\\t<p>Like what you see? <a href=\\\"#\\\">See more posts </a></p>\\t\\t\\n                                    <h2 >\\n                       Download &amp; Enjoy\\n                    </h2>\\n        <p>\\n            Access your courses anywhere,\\n            anytime &amp; prepare with practice tests\\n        </p>\\n        <ul>\\n            <li>\\n                <a href=\\\"#\\\">\\n                    App Store\\n                    Available now on the\\n                </a>\\n            </li>\\n            <li>\\n                <a href=\\\"#\\\">\\n                    Google Play\\n                    Get in on\\n                </a>\\n            </li>\\n        </ul>\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t<img width=\\\"497\\\" height=\\\"786\\\" src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/call-to-mobile-pic.png\\\" alt=\\\"\\\" srcset=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/call-to-mobile-pic.png 497w, https://demoapus2.com/edumy/wp-content/uploads/2019/06/call-to-mobile-pic-190x300.png 190w, https://demoapus2.com/edumy/wp-content/uploads/2019/06/call-to-mobile-pic-416x658.png 416w\\\" sizes=\\\"(max-width: 497px) 100vw, 497px\\\" />\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t<h2>Need To Train Your Team?</h2>Cum doctus civibus efficiantur in imperdiet deterruisset.\\t\\t\\n                                                                                            <img src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/brand-1.png\\\" alt=\\\"\\\">\\n                                                                                            <img src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/brand-2.png\\\" alt=\\\"\\\">\\n                                                                                            <img src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/brand-3.png\\\" alt=\\\"\\\">\\n                                                                                            <img src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/06/brand-4.png\\\" alt=\\\"\\\">\\n                                                                                            <img src=\\\"https://demoapus2.com/edumy/wp-content/uploads/2019/07/brand-5.png\\\" alt=\\\"\\\">\\n                            <h2>Get Newsletter</h2>\\n                            <p>Your download should start automatically, if not Click here. Should I give up, huh?</p>\\n            <!-- Mailchimp for WordPress v4.5.2 - https://wordpress.org/plugins/mailchimp-for-wp/ --><form id=\\\"mc4wp-form-1\\\" method=\\\"post\\\" data-id=\\\"805\\\" data-name=\\\"\\\" ><p>\\n\\t<input type=\\\"email\\\" name=\\\"EMAIL\\\" placeholder=\\\"Your email address\\\" required />  \\t\\n  \\t<button type=\\\"submit\\\">\\n    \\tGet it now \\n  \\t</button>\\n</p><label style=\\\"display: none !important;\\\">Leave this field empty if you're human: <input type=\\\"text\\\" name=\\\"_mc4wp_honeypot\\\" value=\\\"\\\" tabindex=\\\"-1\\\" autocomplete=\\\"off\\\" /></label><input type=\\\"hidden\\\" name=\\\"_mc4wp_timestamp\\\" value=\\\"1563446100\\\" /><input type=\\\"hidden\\\" name=\\\"_mc4wp_form_id\\\" value=\\\"805\\\" /><input type=\\\"hidden\\\" name=\\\"_mc4wp_form_element_id\\\" value=\\\"mc4wp-form-1\\\" /></form><!-- / Mailchimp for WordPress Plugin -->\",\"post_title\":\"Home\",\"post_excerpt\":\"\",\"post_status\":\"publish\",\"comment_status\":\"closed\",\"ping_status\":\"closed\",\"post_password\":\"\",\"post_name\":\"home\",\"to_ping\":\"\",\"pinged\":\"\",\"post_modified\":\"2019-05-16 02:41:58\",\"post_modified_gmt\":\"2019-05-16 02:41:58\",\"post_content_filtered\":\"\",\"post_parent\":0,\"guid\":\"http://*************/wordpress-work/edumy/?page_id=288\",\"menu_order\":0,\"post_type\":\"page\",\"post_mime_type\":\"\",\"comment_count\":\"0\",\"filter\":\"raw\"})"} []
[2023-08-14 12:58:59] merlin-logger.DEBUG: The blog page was set {"blog_page_id":"[object] (WP_Post: {\"ID\":308,\"post_author\":\"1\",\"post_date\":\"2019-05-16 04:01:12\",\"post_date_gmt\":\"2019-05-16 04:01:12\",\"post_content\":\"\",\"post_title\":\"Blog\",\"post_excerpt\":\"\",\"post_status\":\"publish\",\"comment_status\":\"closed\",\"ping_status\":\"closed\",\"post_password\":\"\",\"post_name\":\"blog\",\"to_ping\":\"\",\"pinged\":\"\",\"post_modified\":\"2019-05-16 04:01:12\",\"post_modified_gmt\":\"2019-05-16 04:01:12\",\"post_content_filtered\":\"\",\"post_parent\":0,\"guid\":\"http://*************/wordpress-work/edumy/contact-copy/\",\"menu_order\":0,\"post_type\":\"page\",\"post_mime_type\":\"\",\"comment_count\":\"0\",\"filter\":\"raw\"})"} []
[2023-08-14 12:59:02] merlin-logger.INFO: The content import AJAX call will be executed with this import data {"title":"Elementor Widgets","data":"/home/<USER>/public_html/wordpress/wp-content/uploads/2023/08/widgets-2023-08-14__12-58-32.json"} []
[2023-08-14 12:59:02] merlin-logger.DEBUG: STM LMS Sidebar :   STM LMS Popular Courses - Popular Courses - Imported   [] []
[2023-08-14 12:59:05] merlin-logger.INFO: The content import AJAX call will be executed with this import data {"title":"Theme Options","data":"/home/<USER>/public_html/wordpress/wp-content/uploads/2023/08/options-2023-08-14__12-58-32.dat"} []
[2023-08-14 12:59:05] merlin-logger.INFO: The customizer import has finished successfully [] []
[2023-08-14 12:59:07] merlin-logger.INFO: The content import AJAX call will be executed with this import data {"title":"After import setup","data":0} []
[2023-08-14 12:59:13] merlin-logger.DEBUG: The child theme installation step has been displayed [] []
[2023-08-14 12:59:19] merlin-logger.DEBUG: The child theme style.css content was generated [] []
[2023-08-14 12:59:19] merlin-logger.DEBUG: The child theme functions.php content was generated [] []
[2023-08-14 12:59:19] merlin-logger.DEBUG: The child theme screenshot was copied to the child theme, with the following result {"copied":true} []
[2023-08-14 12:59:19] merlin-logger.DEBUG: The newly generated child theme was activated [] []
[2023-08-14 12:59:25] merlin-logger.DEBUG: The final step has been displayed [] []
