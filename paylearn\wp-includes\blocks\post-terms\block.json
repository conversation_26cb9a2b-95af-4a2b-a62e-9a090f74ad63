{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "core/post-terms", "title": "Post Terms", "category": "theme", "description": "Post terms.", "textdomain": "default", "attributes": {"term": {"type": "string"}, "textAlign": {"type": "string"}, "separator": {"type": "string", "default": ", "}, "prefix": {"type": "string", "default": ""}, "suffix": {"type": "string", "default": ""}}, "usesContext": ["postId", "postType"], "example": {"viewportWidth": 350}, "supports": {"html": false, "color": {"gradients": true, "link": true, "__experimentalDefaultControls": {"background": true, "text": true, "link": true}}, "spacing": {"margin": true, "padding": true}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalFontWeight": true, "__experimentalFontStyle": true, "__experimentalTextTransform": true, "__experimentalTextDecoration": true, "__experimentalLetterSpacing": true, "__experimentalDefaultControls": {"fontSize": true}}, "interactivity": {"clientNavigation": true}, "__experimentalBorder": {"radius": true, "color": true, "width": true, "style": true, "__experimentalDefaultControls": {"radius": true, "color": true, "width": true, "style": true}}}, "style": "wp-block-post-terms"}