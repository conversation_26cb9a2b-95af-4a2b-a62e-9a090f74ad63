.wp-block-post-featured-image{
  margin-left:0;
  margin-right:0;
}
.wp-block-post-featured-image a{
  display:block;
  height:100%;
}
.wp-block-post-featured-image :where(img){
  box-sizing:border-box;
  height:auto;
  max-width:100%;
  vertical-align:bottom;
  width:100%;
}
.wp-block-post-featured-image.alignfull img,.wp-block-post-featured-image.alignwide img{
  width:100%;
}
.wp-block-post-featured-image .wp-block-post-featured-image__overlay.has-background-dim{
  background-color:#000;
  inset:0;
  position:absolute;
}
.wp-block-post-featured-image{
  position:relative;
}

.wp-block-post-featured-image .wp-block-post-featured-image__overlay.has-background-gradient{
  background-color:initial;
}
.wp-block-post-featured-image .wp-block-post-featured-image__overlay.has-background-dim-0{
  opacity:0;
}
.wp-block-post-featured-image .wp-block-post-featured-image__overlay.has-background-dim-10{
  opacity:.1;
}
.wp-block-post-featured-image .wp-block-post-featured-image__overlay.has-background-dim-20{
  opacity:.2;
}
.wp-block-post-featured-image .wp-block-post-featured-image__overlay.has-background-dim-30{
  opacity:.3;
}
.wp-block-post-featured-image .wp-block-post-featured-image__overlay.has-background-dim-40{
  opacity:.4;
}
.wp-block-post-featured-image .wp-block-post-featured-image__overlay.has-background-dim-50{
  opacity:.5;
}
.wp-block-post-featured-image .wp-block-post-featured-image__overlay.has-background-dim-60{
  opacity:.6;
}
.wp-block-post-featured-image .wp-block-post-featured-image__overlay.has-background-dim-70{
  opacity:.7;
}
.wp-block-post-featured-image .wp-block-post-featured-image__overlay.has-background-dim-80{
  opacity:.8;
}
.wp-block-post-featured-image .wp-block-post-featured-image__overlay.has-background-dim-90{
  opacity:.9;
}
.wp-block-post-featured-image .wp-block-post-featured-image__overlay.has-background-dim-100{
  opacity:1;
}
.wp-block-post-featured-image:where(.alignleft,.alignright){
  width:100%;
}